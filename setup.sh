#!/bin/bash
set -x
export ENV="$1"
export VERSION="$2"
export APP="$3"
export BRANCH="$4"
export CLUSTER_IDENTIFIER="$5"
export regions="$6"
export tenant_service_url="${7}"

echo "${ENV}"
echo "${VERSION}"
echo "${APP}"
echo "${BRANCH}"

export POD_NAME=b2b
export DOCKER_TAG_NAME=${POD_NAME}_${ENV}_${APP}
echo "${DOCKER_TAG_NAME}"

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $nonTreeboTenants
}

if  [[ "$ENV" == "production" || "$ENV" == "staging" ]]; then
    loadActiveTenants
    echo "${ENV} env"
    export ENV_FILE=/opt/${APP}/envrepo/finance_erp/environment/${ENV}.env

    export "$(grep -v '^#' ${ENV_FILE} | xargs)"
    echo "Setting up nginx container"
    lsof -i :80 | awk '{print $2}' | grep -v 'PID' | xargs -I {} kill -9 {}
    docker-compose -f /opt/"${APP}"/deployment/nginx-docker-compose.yml up -d

    echo "Setting up app container"
    docker-compose -f /opt/"${APP}"/deployment/app-compose.yml up -d

    echo "Setting up Async Job Executor"
    for tenant_id in ${allTenants[@]}; do
          export TENANT_ID=$tenant_id
          docker-compose -f /opt/"${APP}"/deployment/job-executor-compose.yml -p finance_erp_${TENANT_ID} up -d
    done
fi
