install python 3.9 and Psql


###Local Setup
```commandline
pip install -r requirements/dev.txt
pre-commit install
```

####DB config

```
psql postgres
CREATE DATABASE finance_erp;
CREATE USER finance_user WITH ENCRYPTED PASSWORD 'yourpass';
GRANT pg_read_all_data TO finance_user;
GRANT pg_write_all_data TO finance_user;# there are more safer options
```
Connect to db and run migration file

####Update env config
```dotenv
DB_HOST=localhost
DB_NAME=finance_erp
DB_PASSWORD=<>
DB_USER=finance_user
DB_PORT=5432
RESELLER_SERVICE_ENDPOINT_URL=https://reseller.treebo.be
NOTIFICATION_SERVICE_URL=http://notification-staging.treebo.be
CRS_SERVICE_URL=http://crs.treebo.be
ALL_E_TECH_ENDPOINT_URL=NOTAVAILABLE
```

For Pycharm add these variables in a local.env file and link this to debugger configuration > EnvFile Tab

for local setup simply run 'runserver.py'


###Build
```bash
docker build --build-arg req_file=requirements/production.txt
```


### Deployment
```bash
docker-compose -f deployment/app-compose.yml up -d
setup.sh <ENV> <BUILD_NUMBER> finance_erp <BRANCH> <CLUSTER_IDENTIFIER> <regions>
```
