import logging

import click
from flask.cli import with_appcontext
from ths_common.constants.user_constants import UserType
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from core.common.constants import PrivilegeCodes
from finance_erp.common.constants import EMAIL_REGEX
from finance_erp.common.decorators import session_manager
from finance_erp.domain.auth.entity.privilege import Privilege
from finance_erp.domain.auth.entity.role import Role
from finance_erp.domain.auth.entity.user_group import UserGroup
from finance_erp.domain.auth.repository.privilege_repository import PrivilegesRepository
from finance_erp.domain.auth.repository.role_repository import RolesRepository
from finance_erp.domain.auth.repository.user_group_repository import UserGroupRepository
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command(name="create-admin-user")
@click.option("--email", required=True, help="User Email", type=str)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run",
    default=TenantClient.get_default_tenant(),
)
@session_manager(commit=True)
@with_appcontext
@inject(
    user_group_repository=UserGroupRepository,
    roles_repository=RolesRepository,
    privileges_repository=PrivilegesRepository,
)
def create_admin_user(
    user_group_repository, roles_repository, privileges_repository, email, tenant_id
):
    request_context.tenant_id = tenant_id
    if not EMAIL_REGEX.match(email):
        raise Exception("Invalid Email")
    role = roles_repository.save_role(Role(name=UserType.SUPER_ADMIN.value))
    privilege = privileges_repository.save_privilege(
        Privilege(name=PrivilegeCodes.CAN_MANAGE_ACCESS_CONTROLS)
    )
    if not roles_repository.is_privilege_mapped_to_role(role.id, privilege.id):
        roles_repository.map_privilege_to_role(role.id, privilege.id)
    user_group_repository.save_user(UserGroup(email=email, role_id=role.id))
    logger.info("Created UserGroup with super-admin role: %s", email)
