import csv
import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.common.decorators import session_manager
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command(name="update-transaction-master-entries")
@click.option(
    "--file_path",
    help="Path to the CSV file with updated transaction master data",
    required=True,
)
@click.option(
    "--hotel_id",
    help="Hotel ID for which transaction master data needs to be updated",
    required=True,
)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    transaction_master_repository=TransactionMasterRepository,
)
def update_transaction_master_entries(
    transaction_master_repository, file_path, hotel_id, tenant_id
):
    """
    CLI command to update transaction master entries for a given hotel and tenant.

    :param transaction_master_repository: Repository to manage transaction master data.
    :param file_path: Path to the CSV file containing updated transaction master data.
    :param hotel_id: ID of the hotel for which the update is performed.
    :param tenant_id: ID of the tenant for multi-tenancy support.
    """
    request_context.tenant_id = tenant_id
    transaction_updates = _read_transaction_data_from_csv(file_path)

    try:
        _update_transaction_master_entries(
            transaction_master_repository, hotel_id, transaction_updates
        )
        logger.info("Transaction Master updated successfully")
    except Exception as e:
        logger.exception(f"Failed to update transaction master due to: {str(e)}")


def _read_transaction_data_from_csv(file_path):
    """
    Reads transaction update data from a CSV file using DictReader.

    :param file_path: Path to the CSV file.
    :return: Dictionary mapping transaction IDs to their updated data.
    """
    transaction_data = {}
    with open(file_path, "r") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            transaction_id = row.get("transaction_id")
            transaction_data[transaction_id] = {
                "gl_code": row.get("gl_code") or None,
                "particulars": row.get("particulars") or None,
            }
    return transaction_data


@session_manager(commit=True)
def _update_transaction_master_entries(
    transaction_master_repository, hotel_id, transaction_updates
):
    """
    Updates transaction master entries based on the provided updates.

    :param transaction_master_repository: Repository to manage transaction master data.
    :param hotel_id: ID of the hotel for which the update is performed.
    :param transaction_updates: Dictionary of transaction updates.
    """
    existing_transactions = transaction_master_repository.load_all(hotel_id)
    updated_transaction_ids = []

    for transaction in existing_transactions:
        transaction_id = str(transaction.transaction_id)
        if transaction_id in transaction_updates:
            updates = transaction_updates[transaction_id]
            is_updated = False

            if transaction.gl_code != updates["gl_code"]:
                transaction.gl_code = updates["gl_code"]
                transaction.is_dirty = True
                is_updated = True

            if transaction.particulars != updates["particulars"]:
                transaction.particulars = updates["particulars"]
                transaction.is_dirty = True
                is_updated = True

            if is_updated:
                updated_transaction_ids.append(transaction_id)

    logger.info(f"Transactions updated: {updated_transaction_ids}")
    transaction_master_repository.create_or_update_records_bulk(existing_transactions)
