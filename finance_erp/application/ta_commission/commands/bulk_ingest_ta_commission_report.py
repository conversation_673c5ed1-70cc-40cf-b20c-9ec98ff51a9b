import logging
from decimal import Decimal
from typing import List

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.common.constants import NavisionReports, TACommissionEntryType
from finance_erp.common.utils.utils import group_by_attribute
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from finance_erp.domain.ta_commission.entity.ta_commission import TACommissionEntity
from finance_erp.domain.ta_commission.factory.ta_commission_factory import (
    TACommissionEntityFactory,
)
from finance_erp.domain.ta_commission.repository.ta_commission_data_repository import (
    TACommissionReportRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[TACommissionReportRepository, IngestionAuditService])
class BulkIngestTACommissionReportCommandHandler(BaseIngestionCommandHandler):
    def __init__(
        self,
        ta_commission_reports_repository: TACommissionReportRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        self.ta_commission_reports_repository = ta_commission_reports_repository
        super().__init__(ingestion_audit_service)

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        (
            ta_commission_aggregates_to_update,
            new_ta_commission_aggregates,
        ) = self._segregate_records(ingestion_data_list, event_id)

        if new_ta_commission_aggregates:
            self.ta_commission_reports_repository.insert_many(
                new_ta_commission_aggregates
            )
        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(new_ta_commission_aggregates)),
        )
        if ta_commission_aggregates_to_update:
            self.ta_commission_reports_repository.bulk_update_records(
                ta_commission_aggregates_to_update
            )
        self.ingestion_audit_service.record_ingestion_data_update_event(
            event_id,
            report_name,
            stats=dict(no_of_updated_records=len(ta_commission_aggregates_to_update)),
        )
        return f"Successfully ingested {report_name} event {event_id}"

    def _segregate_records(self, report_dicts: list, event_id):
        (
            already_pushed_reports,
            non_pushed_report_dicts,
        ) = self._filter_out_already_pushed_records(report_dicts)
        uuids_of_already_pushed_reports = {
            report.get_unique_identifier() for report in already_pushed_reports
        }

        reference_number = self._get_unique_ids(non_pushed_report_dicts)
        updatable_existing_reports = (
            self.ta_commission_reports_repository.get_by_reference_numbers(
                reference_number, for_update=True
            )
        )
        uuids_of_updatable_existing_record = set()
        booking_ref_to_updatable_ta_cm_record_map = dict()
        for report in updatable_existing_reports:
            if report.entry_type == TACommissionEntryType.CREDIT:
                uuids_of_updatable_existing_record.add(report.get_unique_identifier())
                booking_ref_to_updatable_ta_cm_record_map[
                    report.reference_number
                ] = report
        data_to_update, data_to_insert, uuids_of_new_records_to_insert = [], [], set()

        for item in non_pushed_report_dicts:
            uu_id = self._get_unique_id(item)
            record = booking_ref_to_updatable_ta_cm_record_map.get(
                item["reference_number"]
            )
            if record:
                record.update(item)
                data_to_update.append(record)
            elif item.get("commission_amount") != Decimal("0"):
                uuids_of_new_records_to_insert.add(uu_id)
                data_to_insert.append(
                    TACommissionEntityFactory.create_ta_commission_data_from_data_dict(
                        item
                    )
                )
        _new_records, _records_to_update = self.nullify_old_entries_by_debit(
            already_pushed_reports, report_dicts
        )
        data_to_insert.extend(_new_records)
        data_to_update.extend(_records_to_update)
        self._record_report_filtering_stats(
            uuids_of_already_pushed_reports,
            reference_number,
            uuids_of_updatable_existing_record,
            uuids_of_new_records_to_insert,
            event_id,
        )
        return data_to_update, data_to_insert

    def nullify_old_entries_by_debit(
        self, already_pushed_reports: List[TACommissionEntity], report_dicts: List[dict]
    ):
        group_by_booking_reference_id = group_by_attribute(
            already_pushed_reports, "reference_number"
        )
        new_records, records_to_update = [], []
        for modified_entry in report_dicts:
            booking_ref_number = modified_entry["reference_number"]
            if booking_ref_number in group_by_booking_reference_id:
                _new_records, _records_to_update = self.nullify_ta_commission_data(
                    group_by_booking_reference_id[booking_ref_number], modified_entry
                )
                new_records.extend(_new_records)
                records_to_update.extend(_records_to_update)
        return new_records, records_to_update

    @staticmethod
    def nullify_ta_commission_data(
        old_ta_commission_data_on_bookings: List[TACommissionEntity], new_record: dict
    ):
        pretax_room_rent, commission_amount = 0, 0
        records_to_insert, records_to_update = [], []

        latest_existing_credit_record = sorted(
            [
                rc
                for rc in old_ta_commission_data_on_bookings
                if rc.entry_type == TACommissionEntryType.CREDIT
            ],
            key=lambda a: a.created_at,
            reverse=True,
        )[0]
        if latest_existing_credit_record.commission_amount == new_record.get(
            "commission_amount"
        ) and latest_existing_credit_record.ta_sh_profile_code == new_record.get(
            "ta_sh_profile_code"
        ):
            return records_to_insert, records_to_update
        for commission in old_ta_commission_data_on_bookings:
            if commission.entry_type == TACommissionEntryType.CREDIT:
                pretax_room_rent += commission.pretax_room_rent
                commission_amount += commission.commission_amount
            else:
                pretax_room_rent -= commission.pretax_room_rent
                commission_amount -= commission.commission_amount
        if not latest_existing_credit_record.pushed:
            latest_existing_credit_record.update(new_record)
            return records_to_insert, [latest_existing_credit_record]
        records_to_insert = [
            TACommissionEntityFactory.create_debit_entry_from_domain_entity(
                latest_existing_credit_record,
                pretax_room_rent,
                commission_amount,
                new_record["posting_date"],
            ),
            TACommissionEntityFactory.create_ta_commission_data_from_data_dict(
                new_record
            ),
        ]
        return records_to_insert, records_to_update

    def _filter_out_already_pushed_records(self, report_dicts):
        booking_ref_numbers = self._get_unique_ids(report_dicts)
        already_pushed_reports = self.ta_commission_reports_repository.get_pushed_records_by_booking_reference_numbers(
            reference_numbers=booking_ref_numbers
        )
        already_pushed_report_pool = {
            report.reference_number for report in already_pushed_reports
        }
        non_pushed_report_dicts = [
            report
            for report in report_dicts
            if report["reference_number"] not in already_pushed_report_pool
        ]
        return already_pushed_reports, non_pushed_report_dicts

    def _record_report_filtering_stats(
        self,
        uuids_of_already_pushed_reports,
        uuids_of_nn_pushed_records,
        uuids_of_updatable_existing_record,
        uuids_of_new_records_to_insert,
        event_id,
    ):
        stats = dict(
            already_pushed_reports=uuids_of_already_pushed_reports,
            count_of_pushed_records=len(uuids_of_already_pushed_reports),
            records_elligible_for_ingestion=uuids_of_nn_pushed_records,
            elligible_count=len(uuids_of_nn_pushed_records),
            new_records=uuids_of_new_records_to_insert,
            new_records_count=len(uuids_of_new_records_to_insert),
            updatable_existing_record=uuids_of_updatable_existing_record,
            updatable_existing_record_count=len(uuids_of_updatable_existing_record),
        )
        self.ingestion_audit_service.record_ingestion_data_filter_event(
            event_id, NavisionReports.TA_COMMISSION_REPORT, stats
        )

    # use booking ref as identifier even if we have unique ref id
    def _get_unique_ids(self, ingestion_data_list):
        return [self._get_unique_id(rc) for rc in ingestion_data_list]

    def _get_unique_id(self, ingestion_data):
        return ingestion_data["reference_number"]

    def _parse_job_data(self, request_data):
        from finance_erp.common.schema.ta_commission import TACommissionBaseSchema

        return [TACommissionBaseSchema(**data).model_dump() for data in request_data]
