from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.ta_commission.dtos.get_ta_commission_report_dto import (
    GetTACommissionReportDto,
)
from finance_erp.domain.ta_commission.repository.ta_commission_data_repository import (
    TACommissionReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[TACommissionReportRepository])
class FetchTACommissionsCommandHandler:
    def __init__(self, ta_commission_reports_repository=TACommissionReportRepository):
        self.ta_commission_reports_repository = ta_commission_reports_repository

    def handle(self, input_criteria: GetTACommissionReportDto):
        if input_criteria.reference_number:
            input_criteria.reference_number = (
                input_criteria.reference_number.split(",")
                if input_criteria.reference_number
                else None
            )
            return self.ta_commission_reports_repository.get_by_reference_numbers(
                input_criteria.reference_number
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.ta_commission_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
