from finance_erp.async_job.handlers.data_aggregation_handler import (
    DataAggregationGenericJobHandler,
)
from finance_erp.async_job.handlers.data_ingestion_handler import (
    DataIngestGenericJobHandler,
)
from finance_erp.async_job.handlers.data_pull_handler import DataPullGenericJobHandler
from finance_erp.async_job.handlers.data_push_handler import DataPushGenericJobHandler
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from object_registry import locate_instance

job_registry = locate_instance(JobRegistry)

job_registry.register(
    JobName.DATA_PUSH_JOB_NAME, locate_instance(DataPushGenericJobHandler).handle
)

job_registry.register(
    JobName.DATA_PULL_JOB_NAME, locate_instance(DataPullGenericJobHandler).handle
)

job_registry.register(
    JobName.DATA_INGESTION_JOB_NAME,
    locate_instance(DataIngestGenericJobHandler).handle,
)

job_registry.register(
    JobName.DATA_AGGREGATION,
    locate_instance(DataAggregationGenericJobHandler).handle,
)
