from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.settlement.expense.entity.expense import (
    UPDATABLE_FIELDS,
    ExpenseEntity,
)
from finance_erp.domain.settlement.expense.repository.expense_repository import (
    ExpenseRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[ExpenseRepository])
class BulkUpdateExpenseReportCommandHandler:
    def __init__(self, settlement_expense_reports_repository: ExpenseRepository):
        self.settlement_expense_reports_repository = (
            settlement_expense_reports_repository
        )

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        settlement_expense_report_reports: List[
            ExpenseEntity
        ] = self.settlement_expense_reports_repository.get_by_ids(
            uuids, for_update=True
        )
        report_map = {ag.uu_id: ag for ag in settlement_expense_report_reports}
        settlement_expense_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in report_map:
                raise ResourceNotFound("ExpenseRecord", extra_payload=dict(uu_id=uu_id))
            settlement_expense_report_report: ExpenseEntity = report_map[uu_id]
            if settlement_expense_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(uu_id=uu_id),
                )
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(settlement_expense_report_report, attr, request_data[attr])
            settlement_expense_report_reports.append(settlement_expense_report_report)
        self.settlement_expense_reports_repository.bulk_update_records(
            settlement_expense_report_reports
        )
        return settlement_expense_report_reports
