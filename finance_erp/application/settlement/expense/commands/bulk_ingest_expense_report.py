import logging

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.domain.settlement.expense.factory.expense_entity_factory import (
    ExpenseFactory,
)
from finance_erp.domain.settlement.expense.repository.expense_repository import (
    ExpenseRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[ExpenseRepository, IngestionAuditService])
class BulkIngestExpenseReportCommandHandler(BaseIngestionCommandHandler):
    def __init__(
        self,
        settlement_expense_reports_repository: ExpenseRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        self.settlement_expense_reports_repository = (
            settlement_expense_reports_repository
        )
        super().__init__(ingestion_audit_service)

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        (
            settlement_expense_reports_to_update,
            new_settlement_expense_reports,
        ) = self._segregate_records(ingestion_data_list, event_id)

        if new_settlement_expense_reports:
            self.settlement_expense_reports_repository.insert_many(
                new_settlement_expense_reports
            )
        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(new_settlement_expense_reports)),
        )
        if settlement_expense_reports_to_update:
            self.settlement_expense_reports_repository.bulk_update_records(
                settlement_expense_reports_to_update
            )
        self.ingestion_audit_service.record_ingestion_data_update_event(
            event_id,
            report_name,
            stats=dict(no_of_updated_records=len(settlement_expense_reports_to_update)),
        )
        return f"Successfully ingested {report_name} event {event_id}"

    def _segregate_records(self, report_dicts: list, event_id):
        (
            uuids_of_already_pushed_reports,
            non_pushed_report_dicts,
        ) = self._filter_out_already_pushed_records(report_dicts)
        uuids_of_nn_pushed_records = self._get_unique_ids(non_pushed_report_dicts)
        updatable_existing_reports = (
            self.settlement_expense_reports_repository.get_by_ids(
                uuids_of_nn_pushed_records, for_update=True
            )
        )
        uuids_of_updatable_existing_record = {
            report.get_unique_identifier() for report in updatable_existing_reports
        }
        uuids_to_record_map = {
            report.get_unique_identifier(): report
            for report in updatable_existing_reports
        }
        data_to_update, data_to_insert, uuids_of_new_records_to_insert = [], [], set()
        for item in non_pushed_report_dicts:
            uu_id = self._get_uu_id(item)
            if uu_id in uuids_of_updatable_existing_record:
                record = uuids_to_record_map.get(uu_id)
                record.update(item)
                data_to_update.append(record)
            else:
                uuids_of_new_records_to_insert.add(uu_id)
                data_to_insert.append(
                    ExpenseFactory.create_settlement_expense_from_data_dict(item)
                )
        self._record_report_filtering_stats(
            uuids_of_already_pushed_reports,
            uuids_of_nn_pushed_records,
            uuids_of_updatable_existing_record,
            uuids_of_new_records_to_insert,
            event_id,
        )
        return data_to_update, data_to_insert

    def _filter_out_already_pushed_records(self, report_dicts):
        unique_ids = self._get_unique_ids(report_dicts)
        already_pushed_reports = (
            self.settlement_expense_reports_repository.get_pushed_records_by_uuid(
                uu_ids=unique_ids
            )
        )
        already_pushed_report_pool = {
            report.get_unique_identifier() for report in already_pushed_reports
        }
        non_pushed_report_dicts = [
            report
            for report in report_dicts
            if self._get_uu_id(report) not in already_pushed_report_pool
        ]
        uuids_of_already_pushed_reports = {
            report.get_unique_identifier() for report in already_pushed_reports
        }
        return uuids_of_already_pushed_reports, non_pushed_report_dicts

    def _record_report_filtering_stats(
        self,
        uuids_of_already_pushed_reports,
        uuids_of_nn_pushed_records,
        uuids_of_updatable_existing_record,
        uuids_of_new_records_to_insert,
        event_id,
    ):
        stats = dict(
            already_pushed_reports=uuids_of_already_pushed_reports,
            count_of_pushed_records=len(uuids_of_already_pushed_reports),
            records_elligible_for_ingestion=uuids_of_nn_pushed_records,
            elligible_count=len(uuids_of_nn_pushed_records),
            new_records=uuids_of_new_records_to_insert,
            new_records_count=len(uuids_of_new_records_to_insert),
            updatable_existing_record=uuids_of_updatable_existing_record,
            updatable_existing_record_count=len(uuids_of_updatable_existing_record),
        )
        self.ingestion_audit_service.record_ingestion_data_filter_event(
            event_id, NavisionReports.EXPENSE_REPORT, stats
        )

    def _get_unique_ids(self, ingestion_data_list):
        return [
            f'{rc["entry_type"]}/{rc.get("hotel_code")}/{rc.get("posting_date")}'
            for rc in ingestion_data_list
        ]

    @staticmethod
    def _get_uu_id(data):
        return (
            f'{data["entry_type"]}/{data.get("hotel_code")}/{data.get("posting_date")}'
        )
