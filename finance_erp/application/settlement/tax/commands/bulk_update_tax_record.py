from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.settlement.tax.entity.tax import UPDATABLE_FIELDS, TaxEntity
from finance_erp.domain.settlement.tax.repository.tax_repository import TaxRepository
from object_registry import register_instance


@register_instance(dependencies=[TaxRepository])
class BulkUpdateTaxReportCommandHandler:
    def __init__(self, settlement_tax_reports_repository: TaxRepository):
        self.settlement_tax_reports_repository = settlement_tax_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        settlement_tax_report_reports: List[
            TaxEntity
        ] = self.settlement_tax_reports_repository.get_by_ids(uuids, for_update=True)
        report_map = {ag.uu_id: ag for ag in settlement_tax_report_reports}
        settlement_tax_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in report_map:
                raise ResourceNotFound("TaxRecord", extra_payload=dict(uu_id=uu_id))
            settlement_tax_report_report: TaxEntity = report_map[uu_id]
            if settlement_tax_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(uu_id=uu_id),
                )
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(settlement_tax_report_report, attr, request_data[attr])
            settlement_tax_report_reports.append(settlement_tax_report_report)
        self.settlement_tax_reports_repository.bulk_update_records(
            settlement_tax_report_reports
        )
        return settlement_tax_report_reports
