from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.settlement.treebo_fee.entity.treebo_fee import (
    UPDATABLE_FIELDS,
    TreeboFeeEntity,
)
from finance_erp.domain.settlement.treebo_fee.repository.treebo_fee_repository import (
    TreeboFeeRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[TreeboFeeRepository])
class BulkUpdateTreeboFeeReportCommandHandler:
    def __init__(self, settlement_treebo_fee_reports_repository: TreeboFeeRepository):
        self.settlement_treebo_fee_reports_repository = (
            settlement_treebo_fee_reports_repository
        )

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        settlement_treebo_fee_report_reports: List[
            TreeboFeeEntity
        ] = self.settlement_treebo_fee_reports_repository.get_by_ids(
            uuids, for_update=True
        )
        report_map = {ag.uu_id: ag for ag in settlement_treebo_fee_report_reports}
        settlement_treebo_fee_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in report_map:
                raise ResourceNotFound(
                    "TreeboFeeRecord", extra_payload=dict(uu_id=uu_id)
                )
            settlement_treebo_fee_report_report: TreeboFeeEntity = report_map[uu_id]
            if settlement_treebo_fee_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(uu_id=uu_id),
                )
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(
                        settlement_treebo_fee_report_report, attr, request_data[attr]
                    )
            settlement_treebo_fee_report_reports.append(
                settlement_treebo_fee_report_report
            )
        self.settlement_treebo_fee_reports_repository.bulk_update_records(
            settlement_treebo_fee_report_reports
        )
        return settlement_treebo_fee_report_reports
