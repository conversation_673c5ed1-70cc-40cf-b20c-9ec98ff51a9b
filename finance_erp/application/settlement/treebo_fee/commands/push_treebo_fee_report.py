from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.application.settlement.treebo_fee.dtos.bulk_treebo_fee_push_request_dto import (
    BulkTreeboFeePushRequestDto,
)
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.treebo_fee import TreeboFeePushRequestSchema
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.settlement.treebo_fee.entity.treebo_fee import TreeboFeeEntity
from finance_erp.domain.settlement.treebo_fee.repository.treebo_fee_repository import (
    TreeboFeeRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        TreeboFeeRepository,
        NavPushAuditService,
        TenantSettings,
        HotelReportRepository,
    ]
)
class BulkPushTreeboFeeReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        settlement_treebo_fee_reports_repository: TreeboFeeRepository,
        nav_push_audit_service: NavPushAuditService,
        tenant_settings: TenantSettings,
        hotel_reports_repository: HotelReportRepository,
    ):
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            hotel_reports_repository,
            tenant_settings,
        )
        self.settlement_treebo_fee_reports_repository = (
            settlement_treebo_fee_reports_repository
        )
        self.tenant_settings = tenant_settings
        self.hotel_reports_repository = hotel_reports_repository

    def _get_schema_and_json_key(self):
        return TreeboFeePushRequestSchema, DataPushNavKeys.TREEBO_FEE

    def _get_records_to_push(self, request_dto: BulkTreeboFeePushRequestDto):
        if request_dto.uu_ids:
            return self.settlement_treebo_fee_reports_repository.get_by_ids(
                request_dto.uu_ids, for_update=True
            )
        return (
            self.settlement_treebo_fee_reports_repository.get_eligible_records_to_push()
        )

    @session_manager(commit=True)
    def _capture_failure(self, record: TreeboFeeEntity, reason):
        record.capture_data_push_failure(reason)
        self.settlement_treebo_fee_reports_repository.update_record(record)

    @session_manager(commit=True)
    def _capture_success(self, record: TreeboFeeEntity):
        record.capture_data_push_success()
        self.settlement_treebo_fee_reports_repository.update_record(record)

    def _get_request_parser(self):
        return BulkTreeboFeePushRequestDto

    def _update_records(self, records):
        self.settlement_treebo_fee_reports_repository.bulk_update_records(records)
