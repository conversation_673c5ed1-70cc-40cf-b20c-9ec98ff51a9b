from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.settlement.hotel_adjustment.entity.hotel_adjustment import (
    UPDATABLE_FIELDS,
    HotelAdjustmentEntity,
)
from finance_erp.domain.settlement.hotel_adjustment.repository.hotel_adjustment_repository import (
    HotelAdjustmentRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[HotelAdjustmentRepository])
class BulkUpdateHotelAdjustmentReportCommandHandler:
    def __init__(
        self, settlement_hotel_adjustment_reports_repository: HotelAdjustmentRepository
    ):
        self.settlement_hotel_adjustment_reports_repository = (
            settlement_hotel_adjustment_reports_repository
        )

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        settlement_hotel_adjustment_report_reports: List[
            HotelAdjustmentEntity
        ] = self.settlement_hotel_adjustment_reports_repository.get_by_ids(
            uuids, for_update=True
        )
        report_map = {ag.uu_id: ag for ag in settlement_hotel_adjustment_report_reports}
        settlement_hotel_adjustment_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in report_map:
                raise ResourceNotFound(
                    "HotelAdjustmentRecord", extra_payload=dict(uu_id=uu_id)
                )
            settlement_hotel_adjustment_report_report: HotelAdjustmentEntity = (
                report_map[uu_id]
            )
            if settlement_hotel_adjustment_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(uu_id=uu_id),
                )
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(
                        settlement_hotel_adjustment_report_report,
                        attr,
                        request_data[attr],
                    )
            settlement_hotel_adjustment_report_reports.append(
                settlement_hotel_adjustment_report_report
            )
        self.settlement_hotel_adjustment_reports_repository.bulk_update_records(
            settlement_hotel_adjustment_report_reports
        )
        return settlement_hotel_adjustment_report_reports
