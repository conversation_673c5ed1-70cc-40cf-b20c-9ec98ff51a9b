import csv

import werkzeug
from flask_restful import Resource, reqparse


class DownloadAndProcessFile(Resource):
    def __init__(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "data_file", type=werkzeug.datastructures.FileStorage, location="files"
        )
        parser.add_argument("entity_type", type=str, location="form")
        self.req_parser = parser

    def post(self):
        _request = self.req_parser.parse_args(strict=True)
        data_file = _request.get("data_file", None)
        if data_file:
            reader_obj = csv.reader(data_file.read())
            for row in reader_obj:
                print(row)
            return "data"
        else:
            return "No image sent"
