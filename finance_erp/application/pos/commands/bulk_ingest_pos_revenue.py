import logging

from ths_common.utils.collectionutils import chunks

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.domain.pos.factory.pos_revenue_item_factory import (
    POSRevenueItemFactory,
)
from finance_erp.domain.pos.repository.pos_revenue_repository import (
    POSRevenueRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import register_instance


@register_instance(dependencies=[POSRevenueRepository, IngestionAuditService])
class BulkIngestPOSRevenueCommandHandler(BaseIngestionCommandHandler):
    CHUNK_SIZE = 1000

    def __init__(
        self,
        pos_revenue_repository: POSRevenueRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        super().__init__(ingestion_audit_service)
        self.pos_revenue_repository = pos_revenue_repository

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        response = []
        for data_list in chunks(ingestion_data_list, self.CHUNK_SIZE):
            data_to_insert = [
                POSRevenueItemFactory.create_pos_revenue_item_from_data_dict(data)
                for data in data_list
            ]
            self.pos_revenue_repository.insert_many(data_to_insert)
            response.extend(data_to_insert)

        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(ingestion_data_list)),
        )
        return response

    def _get_unique_ids(self, ingestion_data_list):
        # bill id is just a place holder identifier for POS revenue ingestion data list
        # Actual unique identifier is generated by the finance erp system
        return [rc["bill_id"] for rc in ingestion_data_list]
