import logging

from finance_erp.domain.pos.repository.pos_revenue_repository import (
    POSRevenueRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[POSRevenueRepository])
class GetPOSRevenueItemsCommandHandler:
    def __init__(self, pos_revenue_repository: POSRevenueRepository):
        self.pos_revenue_repository = pos_revenue_repository

    def handle(self, filters, limit=None, offset=None):
        pos_revenue_items = self.pos_revenue_repository.filter_by_conditions(
            filters, limit, offset
        )
        return pos_revenue_items
