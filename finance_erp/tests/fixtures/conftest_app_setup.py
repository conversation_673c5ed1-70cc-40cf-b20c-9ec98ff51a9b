import os

import pytest
from dotenv import load_dotenv
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp import FINANCE_ERP_ROOT_DIR
from finance_erp.app import create_app
from finance_erp.domain.back_office.models import (
    AllowanceModel,
    ChargeModel,
    CLLedgerItemModel,
    FolioDetailsModel,
    GLLedgerItemModel,
    LedgersFileRecord,
    PaymentModel,
    TransactionMaster,
)
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.domain.hotel.models import HotelModel
from finance_erp.domain.ota.models import OtaModel
from finance_erp.domain.payment.models import PGPaymentModel
from finance_erp.domain.pos.models import POSRevenueItemModel
from finance_erp.domain.reseller.models import PurchaseInvoiceModel, SaleInvoiceModel
from finance_erp.domain.settlement.expense.models import ExpenseModel
from finance_erp.domain.settlement.hotel_adjustment.models import HotelAdjustmentModel
from finance_erp.domain.settlement.loan.models import LoanModel
from finance_erp.domain.settlement.tax.models import TaxModel
from finance_erp.domain.settlement.treebo_fee.models import TreeboFeeModel
from object_registry import finalize_app_initialization


@pytest.fixture(scope="session")
def load_env():
    test_env = FINANCE_ERP_ROOT_DIR.joinpath("dotenvs/test.env")
    load_dotenv(test_env, override=True)


def ensure_valid_test_env_setup(app):
    assert app.config["TESTING"], "App Config 'TESTING' must be True for running tests"
    assert (
        os.environ.get("APP_ENV") == "testing"
    ), "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()
    assert all(
        db_creds.dbname == "finance_erp"
        for tenant_id, db_creds in database_uris.items()
    ), "Database name should be 'finance_erp' for running tests"
    assert all(
        db_creds.host == "localhost" for tenant_id, db_creds in database_uris.items()
    ), "Database host should be 'localhost' for running tests"


def setup_app_for_test():
    _app = create_app()
    finalize_app_initialization()
    return _app


@pytest.fixture(scope="session", autouse=True)
def app(load_env):
    _app = setup_app_for_test()
    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")
    yield _app

    print("===========> Teardown app fixture")
    ctx.pop()


@pytest.fixture(scope="function", autouse=True)
def clear_data_around_test():
    yield
    db_engine.get_session(None).query(HotelModel).delete()
    db_engine.get_session(None).query(CorporateModel).delete()
    db_engine.get_session(None).query(OtaModel).delete()
    db_engine.get_session(None).query(PGPaymentModel).delete()
    db_engine.get_session(None).query(PurchaseInvoiceModel).delete()
    db_engine.get_session(None).query(SaleInvoiceModel).delete()
    db_engine.get_session(None).query(ExpenseModel).delete()
    db_engine.get_session(None).query(HotelAdjustmentModel).delete()
    db_engine.get_session(None).query(LoanModel).delete()
    db_engine.get_session(None).query(TaxModel).delete()
    db_engine.get_session(None).query(TreeboFeeModel).delete()
    db_engine.get_session(None).query(TransactionMaster).delete()
    db_engine.get_session(None).query(GLLedgerItemModel).delete()
    db_engine.get_session(None).query(CLLedgerItemModel).delete()
    db_engine.get_session(None).query(LedgersFileRecord).delete()
    db_engine.get_session(None).query(PaymentModel).delete()
    db_engine.get_session(None).query(ChargeModel).delete()
    db_engine.get_session(None).query(AllowanceModel).delete()
    db_engine.get_session(None).query(FolioDetailsModel).delete()
    db_engine.get_session(None).query(FolioDetailsModel).delete()
    db_engine.get_session(None).query(POSRevenueItemModel).delete()
    db_engine.get_session(None).commit()


@pytest.fixture(scope="session")
def client(app):
    print("===========> Using client fixture")
    test_client = app.test_client()
    yield test_client
    print("===========> Teardown client fixture")
