from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_loan_settlement,
    create_loan_settlement,
    get_loan_settlement,
    update_loan_settlement,
)
from finance_erp.tests.payload_generator import (
    bulk_push_loan_settlement_payload,
    create_loan_settlement_payload,
    get_loan_settlement_payload,
    update_loan_settlement_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_loan_settlement_api(client):
    create_loan_settlement(client, create_loan_settlement_payload())
    loan_details = get_loan_settlement(client, get_loan_settlement_payload())
    assert loan_details
    assert validate_fields_are_not_null(loan_details)


def test_update_loan_settlement_api(client):
    create_loan_settlement(client, create_loan_settlement_payload())
    update_loan_settlement(client, update_loan_settlement_payload())
    loan_details = get_loan_settlement(client, get_loan_settlement_payload())
    assert loan_details["verified"] is False


def test_bulk_push_loan_settlement_api(client):
    create_loan_settlement(client, create_loan_settlement_payload())
    bulk_push_loan_settlement(client, bulk_push_loan_settlement_payload())
    loan_details = get_loan_settlement(client, get_loan_settlement_payload())
    assert loan_details["status"] == ERPEntityStatus.PUSHED
