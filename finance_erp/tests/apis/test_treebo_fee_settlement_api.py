from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_treebo_fee_settlement,
    create_treebo_fee_settlement,
    get_treebo_fee_settlement,
    update_treebo_fee_settlement,
)
from finance_erp.tests.payload_generator import (
    bulk_push_treebo_fee_settlement_payload,
    create_treebo_fee_settlement_payload,
    get_treebo_fee_settlement_payload,
    update_treebo_fee_settlement_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_treebo_fee_settlement_api(client):
    create_treebo_fee_settlement(client, create_treebo_fee_settlement_payload())
    treebo_fee_settlement_details = get_treebo_fee_settlement(
        client, get_treebo_fee_settlement_payload()
    )
    assert treebo_fee_settlement_details
    assert validate_fields_are_not_null(treebo_fee_settlement_details)


def test_update_treebo_fee_settlement_api(client):
    create_treebo_fee_settlement(client, create_treebo_fee_settlement_payload())
    update_treebo_fee_settlement(client, update_treebo_fee_settlement_payload())
    treebo_fee_settlement_details = get_treebo_fee_settlement(
        client, get_treebo_fee_settlement_payload()
    )
    assert treebo_fee_settlement_details["verified"] is False


def test_bulk_push_treebo_fee_settlement_api(client):
    create_treebo_fee_settlement(client, create_treebo_fee_settlement_payload())
    bulk_push_treebo_fee_settlement(client, bulk_push_treebo_fee_settlement_payload())
    treebo_fee_settlement_details = get_treebo_fee_settlement(
        client, get_treebo_fee_settlement_payload()
    )
    assert treebo_fee_settlement_details["status"] == ERPEntityStatus.PUSHED
