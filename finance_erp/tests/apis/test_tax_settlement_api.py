from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_tax_settlement,
    create_tax_settlement,
    get_tax_settlement,
    update_tax_settlement,
)
from finance_erp.tests.payload_generator import (
    bulk_push_tax_settlement_payload,
    create_tax_settlement_payload,
    get_tax_settlement_payload,
    update_tax_settlement_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_tax_settlement_api(client):
    create_tax_settlement(client, create_tax_settlement_payload())
    tax_settlement_details = get_tax_settlement(client, get_tax_settlement_payload())
    assert tax_settlement_details
    assert validate_fields_are_not_null(tax_settlement_details)


def test_update_tax_settlement_api(client):
    create_tax_settlement(client, create_tax_settlement_payload())
    update_tax_settlement(client, update_tax_settlement_payload())
    tax_settlement_details = get_tax_settlement(client, get_tax_settlement_payload())
    assert tax_settlement_details["verified"] is False


def test_bulk_push_tax_settlement_api(client):
    create_tax_settlement(client, create_tax_settlement_payload())
    bulk_push_tax_settlement(client, bulk_push_tax_settlement_payload())
    tax_settlement_details = get_tax_settlement(client, get_tax_settlement_payload())
    assert tax_settlement_details["status"] == ERPEntityStatus.PUSHED
