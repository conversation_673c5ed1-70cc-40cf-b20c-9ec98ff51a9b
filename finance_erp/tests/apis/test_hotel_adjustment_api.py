from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_hotel_adjustment,
    create_hotel_adjustment,
    get_hotel_adjustment,
    update_hotel_adjustment,
)
from finance_erp.tests.payload_generator import (
    bulk_push_hotel_adjustment_payload,
    create_hotel_adjustment_payload,
    get_hotel_adjustment_payload,
    update_hotel_adjustment_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_hotel_adjustment_api(client):
    create_hotel_adjustment(client, create_hotel_adjustment_payload())
    hotel_adjustment_details = get_hotel_adjustment(
        client, get_hotel_adjustment_payload()
    )
    assert hotel_adjustment_details
    assert validate_fields_are_not_null(hotel_adjustment_details)


def test_update_hotel_adjustment_api(client):
    create_hotel_adjustment(client, create_hotel_adjustment_payload())
    update_hotel_adjustment(client, update_hotel_adjustment_payload())
    hotel_adjustment_details = get_hotel_adjustment(
        client, get_hotel_adjustment_payload()
    )
    assert hotel_adjustment_details["verified"] is False


def test_bulk_push_hotel_adjustment_api(client):
    create_hotel_adjustment(client, create_hotel_adjustment_payload())
    bulk_push_hotel_adjustment(client, bulk_push_hotel_adjustment_payload())
    hotel_adjustment_details = get_hotel_adjustment(
        client, get_hotel_adjustment_payload()
    )
    assert hotel_adjustment_details["status"] == ERPEntityStatus.PUSHED
