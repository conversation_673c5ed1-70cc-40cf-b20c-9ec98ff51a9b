from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_purchase,
    create_purchase,
    get_purchase,
    update_purchase,
)
from finance_erp.tests.payload_generator import (
    bulk_push_purchase_payload,
    create_purchase_payload,
    get_purchase_payload,
    update_purchase_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_purchase_api(client):
    create_purchase(client, create_purchase_payload())
    purchase_details = get_purchase(client, get_purchase_payload())
    assert purchase_details
    assert validate_fields_are_not_null(purchase_details)


def test_update_purchase_api(client):
    create_purchase(client, create_purchase_payload())
    update_purchase(client, update_purchase_payload())
    purchase_details = get_purchase(client, get_purchase_payload())
    assert purchase_details["verified"] is False


def test_bulk_push_purchase_api(client):
    create_purchase(client, create_purchase_payload())
    bulk_push_purchase(client, bulk_push_purchase_payload())
    purchase_details = get_purchase(client, get_purchase_payload())
    assert purchase_details["status"] == ERPEntityStatus.PUSHED
