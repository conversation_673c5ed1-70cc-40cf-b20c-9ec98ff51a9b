from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_corporate,
    create_corporate,
    get_corporate,
    update_corporate,
)
from finance_erp.tests.payload_generator import (
    bulk_push_corporate_payload,
    create_corporate_payload,
    get_corporate_payload,
    update_corporate_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_corporate_api(client):
    create_corporate(client, create_corporate_payload())
    corporate_details = get_corporate(client, get_corporate_payload())
    assert corporate_details
    assert validate_fields_are_not_null(corporate_details)


def test_update_corporate_api(client):
    create_corporate(client, create_corporate_payload())
    update_corporate(client, update_corporate_payload())
    corporate_details = get_corporate(client, get_corporate_payload())
    assert corporate_details["verified"] is False


def test_bulk_push_corporate_api(client):
    create_corporate(client, create_corporate_payload())
    bulk_push_corporate(client, bulk_push_corporate_payload())
    corporate_details = get_corporate(client, get_corporate_payload())
    assert corporate_details["status"] == ERPEntityStatus.PUSHED
