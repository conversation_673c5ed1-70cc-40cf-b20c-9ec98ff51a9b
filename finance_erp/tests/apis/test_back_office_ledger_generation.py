from contextlib import contextmanager
from datetime import datetime, time, timedelta

import pytest
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.utils.dateutils import date_to_ymd_str

from finance_erp.api.back_office.ledgers import LedgersFileView
from finance_erp.application.back_office.comand_handlers.create_daily_ledgers import (
    DailyLedgerCreationCommandHandler,
)
from finance_erp.application.hotel_settings.configs.payment_debto_mapping import (
    PaymentDebtorCodeMappingDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import LedgersFileType
from finance_erp.domain.back_office.constants import (
    FRONT_DESK,
    AccountMasterTypes,
    FolioStatus,
    GLGroupCodes,
    GLRevenueTypes,
    IntegratedERPs,
    PaymentModes,
    RoomLevelSkuDetails,
)
from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.entity.charge_entity import ChargeEntity
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.entity.ledgers_file import LedgersFileEntity
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.value_objects import (
    PaymentSplitDetails,
    TaxDetails,
    TransactionMetaData,
)
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from finance_erp.tests.mockers import generate_backoffice_ledgers

DATE = datetime.strptime("2021-01-01", "%Y-%m-%d").date()
HOTEL_ID = "123690"
CHECKIN_FOR_ADVANCE = datetime.strptime("2021-01-03", "%Y-%m-%d").date()
CHECKOUT = CHECKOUT_FOR_ADVANCE = datetime.strptime("2021-01-05", "%Y-%m-%d").date()
POSTING_DATE_TO_TEST_ADVANCE_TRANSFER = CHECKOUT_FOR_ADVANCE
POSTING_DATE_TO_TEST_REVERSE_CHECKOUT = CHECKOUT_FOR_ADVANCE + timedelta(days=1)
CASH = "Cash"
CREDIT_CARD = "Credit Card"
AMEX_CARD = "Amex Card"
VISA_CARD = "Visa Card"
REC_LATER_CC_PAYMENT = f"{CREDIT_CARD}#{AMEX_CARD}"
FALL_BACK_ACCOUNT, FALL_BACK_ACCOUNT_NUMBER = ("default", "default_acc")
CC_CARD_ACCOUNT, CC_CARD_ACCOUNT_NUMBER = ("credit_card_DTR_3", "cc_DTR_3_acc_no")
PAYMENT_ACC_MAP = [
    {
        "identifier": f"{CREDIT_CARD}#{AMEX_CARD}",
        "account_number": CC_CARD_ACCOUNT_NUMBER,
    },
]
COMPANY_CODE, ACC_NUMBER = ("DTR_1", "DTR_ACC_1")
COMPANY_CODE_WITHOUT_ACCOUNT = "DTR_2"
CITY_LEDGER = PaymentModes.CITY_LEDGER
POS_FOOD_SKU = "food"
POS_HOTEL = "hotel"


@pytest.fixture(scope="function")
def setup_tx_master(txn_master_repo):
    txn_masters = [
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=1,
            gl_code="GL-2001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent for {FRONT_DESK}",
            identifier_name="Room Rent for Front Desk",
            display_name="Room Rent for Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.CHARGE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=100,
            gl_code="GLT-2001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent Tax for {FRONT_DESK} CGST 6%",
            identifier_name="Room Rent for Front Desk",
            display_name="Room Rent for Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.TAX_ON_CHARGE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
                tax_code="CGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=101,
            gl_code="GLT-2002",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent Tax for {FRONT_DESK} SGST 6%",
            identifier_name="Room Rent for Front Desk",
            display_name="Room Rent for Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.TAX_ON_CHARGE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
                tax_code="SGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=2,
            gl_code="GL-2002",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent Allowance for {FRONT_DESK}",
            identifier_name="Room Rent for Allowance Front Desk",
            display_name="Room Rent for Allowance Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.ALLOWANCE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=102,
            gl_code="GLT-2011",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent Allowance for {FRONT_DESK} CGST 6%",
            identifier_name="Room Rent for Front Desk",
            display_name="Room Rent for Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.TAX_ON_ALLOWANCE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
                tax_code="CGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=103,
            gl_code="GLT-2012",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Room Rent for allowance {FRONT_DESK} SGST 6%",
            identifier_name="Room Rent for Front Desk",
            display_name="Room Rent for Front Desk",
            revenue_center=FRONT_DESK,
            identifier=RoomLevelSkuDetails["sku_code"],
            transaction_type=GLRevenueTypes.TAX_ON_ALLOWANCE,
            transaction_metadata=TransactionMetaData(
                sku_category=RoomLevelSkuDetails["sku_category"],
                tax_code="SGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=3,
            gl_code="GL-2003",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food for {POS_HOTEL}",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.CHARGE,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=300,
            gl_code="GLT-4003",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food Tax for {POS_HOTEL} CGST 6%",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.TAX_ON_CHARGE,
            transaction_metadata=TransactionMetaData(
                tax_code="CGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=301,
            gl_code="GLT-4005",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food Tax for {POS_HOTEL} SGST 6%",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.TAX_ON_CHARGE,
            transaction_metadata=TransactionMetaData(
                tax_code="SGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=30,
            gl_code="GL-2030",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food Allowance for {POS_HOTEL}",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.ALLOWANCE,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=500,
            gl_code="GLT-5003",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food allowance Tax for {POS_HOTEL} CGST 6%",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.TAX_ON_ALLOWANCE,
            transaction_metadata=TransactionMetaData(
                tax_code="CGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=501,
            gl_code="GLT-5005",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Food allowance Tax for {POS_HOTEL} SGST 6%",
            identifier_name=f"Food for {POS_HOTEL}",
            display_name=f"Food for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=POS_FOOD_SKU,
            transaction_type=GLRevenueTypes.TAX_ON_ALLOWANCE,
            transaction_metadata=TransactionMetaData(
                tax_code="SGST",
                tax_value="6.00",
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=4,
            gl_code="GL-2004",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {FRONT_DESK}",
            identifier_name=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {FRONT_DESK}",
            display_name=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CREDIT_CARD,
            transaction_type=GLRevenueTypes.PAYMENT,
            transaction_metadata=TransactionMetaData(
                payment_mode_sub_type=AMEX_CARD,
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=5,
            gl_code="GL-2005",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {FRONT_DESK}",
            identifier_name=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {FRONT_DESK}",
            display_name=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CREDIT_CARD,
            transaction_type=GLRevenueTypes.REFUND,
            transaction_metadata=TransactionMetaData(
                payment_mode_sub_type=AMEX_CARD,
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=6,
            gl_code="GL-2006",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CASH} payment for {FRONT_DESK}",
            identifier_name=f"{CASH} payment for {FRONT_DESK}",
            display_name=f"{CASH} payment for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CASH,
            transaction_type=GLRevenueTypes.PAYMENT,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=7,
            gl_code="GL-2007",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CASH} refund for {FRONT_DESK}",
            identifier_name=f"{CASH} refund for {FRONT_DESK}",
            display_name=f"{CASH} refund for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CASH,
            transaction_type=GLRevenueTypes.REFUND,
            transaction_metadata=TransactionMetaData(),
        ),
        # Gl2
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=8,
            gl_code="GL2-001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Aggregated GL guest control GL2",
            identifier_name=f"Aggregated GL guest control GL2",
            display_name=f"Aggregated GL guest control GL2",
            revenue_center=FRONT_DESK,
            identifier=GLGroupCodes.GUEST_GROUP_CODE_AGGREGATE,
            transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=9,
            gl_code="GL6-001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Aggregated GL guest control GL6",
            identifier_name=f"Aggregated GL guest control GL6",
            display_name=f"Aggregated GL guest control GL6",
            revenue_center=FRONT_DESK,
            identifier=GLGroupCodes.DEBTOR_GROUP_CODE_AGGREGATE,
            transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=10,
            gl_code="GL8-001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Aggregated GL guest control GL8",
            identifier_name=f"Aggregated GL guest control GL8",
            display_name=f"Aggregated GL guest control GL8",
            revenue_center=FRONT_DESK,
            identifier=GLGroupCodes.DEPOSIT_GROUP_CODE_AGGREGATE,
            transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=11,
            gl_code="GL-2010",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CITY_LEDGER} payment for {FRONT_DESK}",
            identifier_name=f"{CITY_LEDGER} payment for {FRONT_DESK}",
            display_name=f"{CITY_LEDGER} payment for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CITY_LEDGER,
            transaction_type=GLRevenueTypes.PAYMENT,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=12,
            gl_code="GL-2011",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CITY_LEDGER} refund for {FRONT_DESK}",
            identifier_name=f"{CITY_LEDGER} refund for {FRONT_DESK}",
            display_name=f"{CITY_LEDGER} refund for {FRONT_DESK}",
            revenue_center=FRONT_DESK,
            identifier=CITY_LEDGER,
            transaction_type=GLRevenueTypes.REFUND,
            transaction_metadata=TransactionMetaData(),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=13,
            gl_code="GL-2012",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {POS_HOTEL}",
            identifier_name=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {POS_HOTEL}",
            display_name=f"{CREDIT_CARD} ({AMEX_CARD}) payment for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=CREDIT_CARD,
            transaction_type=GLRevenueTypes.PAYMENT,
            transaction_metadata=TransactionMetaData(
                payment_mode_sub_type=AMEX_CARD,
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=14,
            gl_code="GL-2013",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {POS_HOTEL}",
            identifier_name=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {POS_HOTEL}",
            display_name=f"{CREDIT_CARD} ({AMEX_CARD}) refund for {POS_HOTEL}",
            revenue_center=POS_HOTEL,
            identifier=CREDIT_CARD,
            transaction_type=GLRevenueTypes.REFUND,
            transaction_metadata=TransactionMetaData(
                payment_mode_sub_type=AMEX_CARD,
            ),
        ),
        TransactionMasterEntity(
            hotel_id=HOTEL_ID,
            erp_name=IntegratedERPs.PROLOGIC,
            transaction_id=15,
            gl_code="GLAd-001",
            is_active=True,
            merge_gl_entries=True,
            particulars=f"Customer Advance",
            identifier_name=f"Customer Advance",
            display_name=f"Customer Advance",
            revenue_center=FRONT_DESK,
            identifier=AccountMasterTypes.CUSTOMER_ADVANCE,
            transaction_type=GLRevenueTypes.PAYMENT,
        ),
    ]
    txn_master_repo.insert_many(txn_masters)
    db_engine.get_scoped_session().commit()
    yield


@pytest.fixture(scope="function")
def setup_base_raw_data(
    payment_repo, charge_repo, allowance_repo, pos_item_repo, corporate_repo
):
    def insert_corporate_details():
        corporates = [
            # DTR_1 corp with account number
            CorporateEntity(
                customer_legal_name="ABC Pvt Ltd",
                corporate_code=COMPANY_CODE,
                customer_trading_name="ABC Traders",
                address="123 Business Avenue, Corporate Park",
                city="Mumbai",
                phone_number="+91-**********",
                post_code="400001",
                email="<EMAIL>",
                credit_limit="500000",
                credit_period=30,
                country_code="IN",
                pan="**********",
                state_code="MH",
                gstin="27**********1Z5",
                gst_customer_type="Regular",
                tan_number="MUMT12345E",
                billing_period=2,
                external_account_number=ACC_NUMBER,
            ),
            # DTR_2 corp without account number
            CorporateEntity(
                customer_legal_name="XYZ Pvt Ltd",
                corporate_code=COMPANY_CODE_WITHOUT_ACCOUNT,
                customer_trading_name="XYZ Traders",
                address="321 Business Avenue, Corporate Park",
                city="Mumbai",
                phone_number="+91-**********",
                post_code="400001",
                email="<EMAIL>",
                credit_limit="500000",
                credit_period=30,
                country_code="IN",
                pan="**********",
                state_code="MH",
                gstin="27XYZE1234F1Z5",
                gst_customer_type="Regular",
                tan_number="XYZ12345E",
                billing_period=2,
                external_account_number=None,
            ),
            # Fall back account
            CorporateEntity(
                customer_legal_name="Default",
                corporate_code=FALL_BACK_ACCOUNT,
                customer_trading_name="default Traders",
                address="default",
                city="Mumbai",
                phone_number="+91-**********",
                post_code="400001",
                email="<EMAIL>",
                credit_limit="500000",
                credit_period=30,
                country_code="IN",
                pan="default",
                state_code="MH",
                gstin="default",
                gst_customer_type="Regular",
                tan_number="default",
                billing_period=2,
                external_account_number=FALL_BACK_ACCOUNT_NUMBER,
            ),
            # CC amex corp account
            CorporateEntity(
                customer_legal_name="CC amex corp",
                corporate_code=CC_CARD_ACCOUNT,
                customer_trading_name="CC amex default Traders",
                address="default",
                city="Mumbai",
                phone_number="+91-**********",
                post_code="400001",
                email="<EMAIL>",
                credit_limit="500000",
                credit_period=30,
                country_code="IN",
                pan="default",
                state_code="MH",
                gstin="default",
                gst_customer_type="Regular",
                tan_number="default",
                billing_period=2,
                external_account_number=CC_CARD_ACCOUNT_NUMBER,
            ),
        ]
        corporate_repo.insert_many(corporates)

    def insert_payment():
        advance_payments = [
            # receive later advance
            PaymentEntity(
                uu_id="1",
                bill_id="bill_1",
                payment_id=1,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="payment",
                payment_mode_sub_type=AMEX_CARD,
                payment_mode=CREDIT_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                category="category",
                owner_name="owner_name",
                folio_number=1,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # receive later advance
            PaymentEntity(
                uu_id="1a",
                bill_id="bill_1",
                payment_id=111,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=12,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="payment",
                payment_mode_sub_type=AMEX_CARD,
                payment_mode=CREDIT_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                category="category",
                owner_name="owner_name",
                folio_number=1,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # cash advance
            PaymentEntity(
                uu_id="2",
                bill_id="bill_1",
                payment_id=2,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=2000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="payment",
                payment_mode=CASH,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
                category="category",
                owner_name="owner_name",
                folio_number=1,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # cash advance by company
            PaymentEntity(
                uu_id="3",
                bill_id="bill_1",
                payment_id=3,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=3000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="payment",
                payment_mode=CASH,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
                category="category",
                owner_name="owner_name",
                debtor_code=COMPANY_CODE,
                folio_number=2,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # receive later advance by company
            PaymentEntity(
                uu_id="4",
                bill_id="bill_1",
                payment_id=4,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="payment",
                payment_mode_sub_type=AMEX_CARD,
                payment_mode=CREDIT_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                category="category",
                owner_name="owner_name",
                debtor_code=COMPANY_CODE,
                folio_number=2,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # cash advance refund
            PaymentEntity(
                uu_id="5",
                bill_id="bill_1",
                payment_id=5,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=100,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="refund",
                payment_mode=CASH,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
                category="category",
                owner_name="owner_name",
                folio_number=1,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # receive later advance refund
            PaymentEntity(
                uu_id="6",
                bill_id="bill_1",
                payment_id=6,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=300,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="refund",
                payment_mode_sub_type=AMEX_CARD,
                payment_mode=CREDIT_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                category="category",
                owner_name="owner_name",
                folio_number=1,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # cash advance refund to company
            PaymentEntity(
                uu_id="7",
                bill_id="bill_1",
                payment_id=7,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=100,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="refund",
                payment_mode=CASH,
                debtor_code=COMPANY_CODE,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
                category="category",
                owner_name="owner_name",
                folio_number=2,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
            # receive later advance refund to company
            PaymentEntity(
                uu_id="8",
                bill_id="bill_1",
                payment_id=8,
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=300,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=CHECKIN_FOR_ADVANCE,
                checkout_date=CHECKOUT_FOR_ADVANCE,
                booking_id="booking_1",
                booking_reference_number="booking_1",
                payment_type="refund",
                payment_mode_sub_type=AMEX_CARD,
                payment_mode=CREDIT_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                category="category",
                owner_name="owner_name",
                debtor_code=COMPANY_CODE,
                folio_number=2,
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
            ),
        ]
        non_advance_payment = [
            # receive later
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="21",
                payment_id=1,
                folio_number=1,
                payment_mode=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
            ),
            # cash
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="22",
                payment_id=2,
                folio_number=1,
                payment_mode=CASH,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
            ),
            # cash by company
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="23",
                payment_id=3,
                folio_number=2,
                payment_mode=CASH,
                debtor_code=COMPANY_CODE,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
            ),
            # receive later by company
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="24",
                payment_id=4,
                folio_number=2,
                payment_mode=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                debtor_code=COMPANY_CODE,
            ),
            # receive later refund
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=100,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="25",
                payment_id=5,
                folio_number=1,
                payment_mode=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
            ),
            # cash refund
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=200,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="26",
                payment_id=6,
                folio_number=1,
                payment_mode=CASH,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
            ),
            # cash refund to company
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=300,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="27",
                payment_id=7,
                folio_number=2,
                payment_mode=CASH,
                debtor_code=COMPANY_CODE,
                payment_mode_sub_type=None,
                crs_payment_mode=CASH,
                crs_payment_mode_sub_type=None,
            ),
            # receive later refund to company
            PaymentEntity(
                bill_id="bill_2",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=400,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=DATE + timedelta(days=1),
                booking_id="booking_2",
                booking_reference_number="booking_2",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="28",
                payment_id=8,
                folio_number=2,
                payment_mode=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                debtor_code=COMPANY_CODE,
            ),
            # receive later by company
            PaymentEntity(
                bill_id="bill_4",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE,
                date_of_payment=DATE,
                checkin_date=DATE,
                checkout_date=CHECKOUT,
                booking_id="booking_4",
                booking_reference_number="booking_4",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="41",
                payment_id=1,
                folio_number=1,
                payment_mode=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                crs_payment_mode=CREDIT_CARD,
                crs_payment_mode_sub_type=AMEX_CARD,
                debtor_code=COMPANY_CODE,
            ),
        ]
        city_ledger_payments_on_checkout = [
            # payment without company
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=1000,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="31",
                payment_id=1,
                folio_number=1,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
            ),
            # payment with company
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=2000,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="32",
                payment_id=2,
                folio_number=2,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
                debtor_code=COMPANY_CODE,
            ),
            # payment with company not having account number
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=3000,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="payment",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="33",
                payment_id=3,
                folio_number=3,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
                debtor_code=COMPANY_CODE_WITHOUT_ACCOUNT,
            ),
            # refund without company
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=100,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="34",
                payment_id=4,
                folio_number=1,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
            ),
            # refund with company
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=200,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="35",
                payment_id=5,
                folio_number=2,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
                debtor_code=COMPANY_CODE,
            ),
            # refund with company not having account number
            PaymentEntity(
                bill_id="bill_3",
                payment_split_id=1,
                hotel_id=HOTEL_ID,
                fin_erp_posting_date=DATE,
                amount=300,
                posting_date=DATE - timedelta(days=3),
                date_of_payment=DATE - timedelta(days=3),
                checkin_date=DATE - timedelta(days=1),
                checkout_date=DATE,
                booking_id="booking_3",
                booking_reference_number="booking_3",
                payment_type="refund",
                category="category",
                owner_name="owner_name",
                revenue_center=FRONT_DESK,
                payment_channel="payment_channel",
                payment_ref_id="payment_ref_id",
                uu_id="36",
                payment_id=6,
                folio_number=3,
                payment_mode=CITY_LEDGER,
                payment_mode_sub_type=None,
                crs_payment_mode=CITY_LEDGER,
                crs_payment_mode_sub_type=None,
                debtor_code=COMPANY_CODE_WITHOUT_ACCOUNT,
            ),
        ]
        payment_repo.insert_many(
            advance_payments + non_advance_payment + city_ledger_payments_on_checkout
        )

    def insert_charges_and_allowance():
        charges = [
            ChargeEntity(
                uu_id="1",
                hotel_id=HOTEL_ID,
                bill_id="bill_2",
                charge_id=1,
                charge_split_id=1,
                folio_number=1,
                posttax_amount=1000,
                pretax_amount=800,
                tax_amount=200,
                tax_details=[
                    TaxDetails(
                        percentage=6.00,
                        tax_type="CGST",
                        amount=100,
                    ),
                    TaxDetails(
                        percentage=6.00,
                        tax_type="SGST",
                        amount=100,
                    ),
                ],
                posting_date=DATE,
                fin_erp_posting_date=DATE,
                applicable_business_date=DATE,
                revenue_center=FRONT_DESK,
                category="category",
                owner_name="owner_name",
                item_id=RoomLevelSkuDetails["sku_code"],
                bill_to_type="",
                charge_type="",
                booking_id="",
                sku_category_id="",
                is_inclusion_charge=False,
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="booking_1",
            )
        ]
        allowance = [
            AllowanceEntity(
                uu_id="1",
                hotel_id=HOTEL_ID,
                bill_id="bill_2",
                allowance_id=1,
                charge_id=1,
                charge_split_id=1,
                folio_number=1,
                posttax_amount=100,
                pretax_amount=80,
                tax_amount=20,
                tax_details=[
                    TaxDetails(
                        percentage=6.00,
                        tax_type="CGST",
                        amount=10,
                    ),
                    TaxDetails(
                        percentage=6.00,
                        tax_type="SGST",
                        amount=10,
                    ),
                ],
                posting_date=DATE,
                fin_erp_posting_date=DATE,
                revenue_center=FRONT_DESK,
                category="category",
                owner_name="owner_name",
                item_id=RoomLevelSkuDetails["sku_code"],
                bill_to_type="",
                charge_type="",
                booking_id="",
                sku_category_id="",
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="booking_1",
            )
        ]
        pos_items = [
            POSRevenueItem(
                uu_id="123e4567-e89b-12d3-a456-************",
                interface_id="POS123",
                interface_name="TouchePOS",
                hotel_id=HOTEL_ID,
                bill_id="BILL001",
                reservation_id="RES001",
                amount=500.00,
                tax=50.00,
                sku_category="food",
                hsn_code="2106",
                payment_method=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                pos_bill_date=DATE,
                pos_bill_time=time(18, 30),
                guest_name="John Doe",
                tax_details=[
                    {"tax_code": "CGST", "tax_amount": 25.00, "tax_value": 6.0},
                    {"tax_code": "SGST", "tax_amount": 25.00, "tax_value": 6.0},
                ],
                revenue_center=POS_HOTEL,
                serving_time="Dinner",
                workstation_id="WS001",
                waiter_id="WTR123",
                sku_id=POS_FOOD_SKU,
            ),
            # allowance void
            POSRevenueItem(
                uu_id="321e4567-e89b-12d3-a456-************",
                interface_id="POS123",
                interface_name="TouchePOS",
                hotel_id=HOTEL_ID,
                bill_id="BILL001",
                reservation_id="RES001",
                amount=-50.00,
                tax=-5.00,
                sku_category="food",
                hsn_code="2106",
                payment_method=CREDIT_CARD,
                payment_mode_sub_type=AMEX_CARD,
                pos_bill_date=DATE,
                pos_bill_time=time(18, 30),
                guest_name="John Doe",
                tax_details=[
                    {"tax_code": "CGST", "tax_amount": 2.50, "tax_value": 6.0},
                    {"tax_code": "SGST", "tax_amount": 2.50, "tax_value": 6.0},
                ],
                revenue_center=POS_HOTEL,
                serving_time="Dinner",
                workstation_id="WS001",
                waiter_id="WTR123",
                sku_id=POS_FOOD_SKU,
            ),
        ]
        charge_repo.insert_many(charges)
        allowance_repo.insert_many(allowance)
        pos_item_repo.insert_many(pos_items)

    insert_payment()
    insert_charges_and_allowance()
    insert_corporate_details()
    db_engine.get_scoped_session().commit()
    yield


@pytest.fixture(scope="function")
def setup_checkout_data(folio_repo):
    folio_repo.insert_many(
        [
            # checkout of guest folio having advance payment
            FolioDetailsEntity(
                uu_id="1",
                hotel_id=HOTEL_ID,
                bill_id="bill_1",
                booking_id="booking_1",
                category="",
                owner_name="Primary Guest",
                folio_number=1,
                folio_status=FolioStatus.INVOICE_LOCKED,
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="BRN2024XYZ",
                is_credit_folio=False,
                payment_split_details=[
                    PaymentSplitDetails(
                        payment_id=1, payment_split_id=1, amount=500
                    ),  # payment splits and transferred to folio 2
                    PaymentSplitDetails(
                        payment_id=5, payment_split_id=1, amount=50
                    ),  # refund splits and transferred to folio 2
                    PaymentSplitDetails(
                        payment_id=6, payment_split_id=1, amount=300
                    ),  # refund remain untouched
                    PaymentSplitDetails(
                        payment_id=111, payment_split_id=1, amount=12
                    ),  # payment remain untouched
                    PaymentSplitDetails(
                        payment_id=2, payment_split_id=1, amount=0
                    ),  # completely transferred to folio 2
                    PaymentSplitDetails(
                        payment_id=3, payment_split_id=2, amount=1000
                    ),  # transferred from folio 2
                    PaymentSplitDetails(
                        payment_id=4, payment_split_id=2, amount=800
                    ),  # transferred from folio 2
                    PaymentSplitDetails(
                        payment_id=8, payment_split_id=2, amount=150
                    ),  # refund transferred from folio 2
                ],
                fin_erp_posting_date=POSTING_DATE_TO_TEST_ADVANCE_TRANSFER,
                room_number=305,
            ),
            # checkout of company folio having advance payment
            FolioDetailsEntity(
                uu_id="2",
                hotel_id=HOTEL_ID,
                bill_id="bill_1",
                booking_id="booking_1",
                category="",
                owner_name="Alice Johnson Company",
                folio_number=2,
                folio_status=FolioStatus.INVOICE_LOCKED,
                billed_entity_id=2,
                account_number=1,
                booking_reference_number="BRN2024XYZ",
                is_credit_folio=False,
                payment_split_details=[
                    PaymentSplitDetails(
                        payment_id=1, payment_split_id=2, amount=500
                    ),  # payment transferred from folio 1
                    PaymentSplitDetails(
                        payment_id=5, payment_split_id=2, amount=50
                    ),  # refund transferred from folio 1
                    PaymentSplitDetails(
                        payment_id=2, payment_split_id=2, amount=2000
                    ),  # payment transferred from folio 1
                    PaymentSplitDetails(
                        payment_id=3, payment_split_id=1, amount=2000
                    ),  # payment splits and transferred to folio 2
                    PaymentSplitDetails(
                        payment_id=4, payment_split_id=1, amount=200
                    ),  # payment splits and transferred to folio 2
                    PaymentSplitDetails(payment_id=5, payment_split_id=2, amount=50),
                    PaymentSplitDetails(
                        payment_id=7, payment_split_id=1, amount=100
                    ),  # refund remains untouched
                    PaymentSplitDetails(
                        payment_id=8, payment_split_id=1, amount=150
                    ),  # refund splits and transferred to folio 1
                ],
                fin_erp_posting_date=POSTING_DATE_TO_TEST_ADVANCE_TRANSFER,
                room_number=305,
            ),
            FolioDetailsEntity(
                uu_id="3",
                hotel_id=HOTEL_ID,
                bill_id="bill_4",
                booking_id="booking_4",
                category="",
                owner_name="New India Ins Company",
                folio_number=1,
                folio_status=FolioStatus.INVOICE_LOCKED,
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="IN2024XYZ",
                is_credit_folio=False,
                payment_split_details=[
                    PaymentSplitDetails(
                        payment_id=1, payment_split_id=1, amount=1000
                    ),  # payment transferred from folio 1
                ],
                fin_erp_posting_date=POSTING_DATE_TO_TEST_ADVANCE_TRANSFER,
                room_number=305,
            ),
        ]
    )

    db_engine.get_scoped_session().commit()
    yield


@pytest.fixture(scope="function")
def setup_reverse_checkout_data(folio_repo, charge_repo):
    folio_repo.insert_many(
        [
            FolioDetailsEntity(
                uu_id="1",
                hotel_id=HOTEL_ID,
                bill_id="bill_1",
                booking_id="booking_1",
                category="",
                owner_name="Primary Guest",
                folio_number=1,
                folio_status=FolioStatus.INVOICE_REISSUED,
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="BRN2024XYZ",
                is_credit_folio=False,
                payment_split_details=[
                    PaymentSplitDetails(
                        payment_id=1, payment_split_id=1, amount=500
                    ),  # payment splits and transferred to folio 2 (card advance payment)
                    PaymentSplitDetails(
                        payment_id=5, payment_split_id=1, amount=50
                    ),  # refund splits and transferred to folio 2 (cash advance refund)
                    PaymentSplitDetails(
                        payment_id=6, payment_split_id=1, amount=300
                    ),  # refund remain untouched (card advance refund)
                    PaymentSplitDetails(
                        payment_id=111, payment_split_id=1, amount=12
                    ),  # payment remain untouched (card later advance payment)
                    PaymentSplitDetails(
                        payment_id=2, payment_split_id=1, amount=0
                    ),  # completely transferred to folio 2 (card advance payment)
                    PaymentSplitDetails(
                        payment_id=3, payment_split_id=2, amount=1000
                    ),  # transferred from folio 2 (cash advance payment by company)
                    PaymentSplitDetails(
                        payment_id=4, payment_split_id=2, amount=800
                    ),  # transferred from folio 2 (card advance payment by company)
                    PaymentSplitDetails(
                        payment_id=8, payment_split_id=2, amount=150
                    ),  # refund transferred from folio 2 (card advance refund to company)
                ],
                fin_erp_posting_date=POSTING_DATE_TO_TEST_REVERSE_CHECKOUT,
                room_number=305,
            ),
        ]
    )
    charge_repo.insert_many(
        [
            ChargeEntity(
                uu_id="102",
                hotel_id=HOTEL_ID,
                bill_id="bill_2",
                charge_id=1,
                charge_split_id=1,
                folio_number=1,
                posttax_amount=-1000,
                pretax_amount=-800,
                tax_amount=-200,
                tax_details=[
                    TaxDetails(
                        percentage=6.00,
                        tax_type="CGST",
                        amount=-100,
                    ),
                    TaxDetails(
                        percentage=6.00,
                        tax_type="SGST",
                        amount=-100,
                    ),
                ],
                posting_date=POSTING_DATE_TO_TEST_REVERSE_CHECKOUT,
                fin_erp_posting_date=POSTING_DATE_TO_TEST_REVERSE_CHECKOUT,
                applicable_business_date=POSTING_DATE_TO_TEST_REVERSE_CHECKOUT,
                revenue_center=FRONT_DESK,
                category="category",
                owner_name="owner_name",
                item_id=RoomLevelSkuDetails["sku_code"],
                bill_to_type="",
                charge_type="",
                booking_id="",
                sku_category_id="",
                is_inclusion_charge=False,
                billed_entity_id=1,
                account_number=1,
                booking_reference_number="booking_1",
            )
        ]
    )

    db_engine.get_scoped_session().commit()
    yield


@contextmanager
def mocked_context():
    actual_gl_gen, actual_cl_gen, actual_zip_gen, actual_err_logger = (
        DailyLedgerCreationCommandHandler.generate_gl_ledger_file,
        DailyLedgerCreationCommandHandler.generate_cl_ledger_file,
        DailyLedgerCreationCommandHandler.archive_ledger_files,
        DailyLedgerCreationCommandHandler.generate_and_send_error_logs,
    )
    (
        actual_rec_later_payment,
        actual_payment_debtor_mapping,
        actual_credit_payment_modes,
        actual_erps,
        actual_exclusion_ref,
        actual_bo_fallback_account_number,
        actual_backoffice_hotel_code,
    ) = (
        TenantSettings.get_receive_later_payment_modes,
        TenantSettings.get_payment_debtor_mapping,
        TenantSettings.get_units_credit_pay_modes,
        TenantSettings.get_enabled_backoffice_erps,
        TenantSettings.get_refund_exclusion_list_for_backoffice,
        TenantSettings.get_bo_fallback_account_number,
        TenantSettings.get_backoffice_hotel_code,
    )
    actual_prv_check = LedgersFileView._do_privilege_check

    @staticmethod
    def mocked_generate_gl_ledger_file(
        date, hotel_id, gl_controls, f_path, erp_name, hotel_code
    ):
        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.GUEST_LEDGER_FILE,
            ledger_file_path="test_url",
            ledger_file_name="test_gl_file_name",
        )

    @staticmethod
    def mocked_generate_cl_ledger_file(
        date, hotel_id, cl_controls, f_path, erp_name, hotel_code
    ):
        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.CITY_LEDGER_FILE,
            ledger_file_path="test_url",
            ledger_file_name="test_cl_file_name",
        )

    @staticmethod
    def mocked_archive_ledger_files(date, hotel_id, files, erp_name):
        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.CONSOLIDATED_LEDGER_ARCHIVE,
            ledger_file_path="test_url",
            ledger_file_name="test_zip_file_name",
        )

    def mocked_receive_later_payment_modes(self, hotel_id):
        return [REC_LATER_CC_PAYMENT, CITY_LEDGER]

    def mocked_get_backoffice_hotel_code(self, hotel_id):
        return "mock"

    def mocked_get_enabled_backoffice_erps(self, hotel_id):
        return [IntegratedERPs.PROLOGIC]

    def mocked_payment_debtor_mapping(self, hotel_id):
        return [PaymentDebtorCodeMappingDto(**config) for config in PAYMENT_ACC_MAP]

    def mocked_payment_config(self, hotel_id):
        return {
            "payment": [
                {
                    "payment_method": "paid_by_treebo",
                    "paid_to": "treebo",
                    "allowed_paid_by": ["treebo"],
                },
                {"payment_method": "bills_on_hold", "paid_to": "hotel"},
                {
                    "payment_method": "settlement_against_payment_transfer",
                    "paid_to": "treebo",
                },
                {
                    "payment_method": "city_ledger",
                    "paid_to": "hotel",
                    "allowed_paid_by": ["hotel"],
                },
                {
                    "payment_method": "treebo_corporate_rewards",
                    "paid_to": "treebo",
                    "allowed_paid_by": ["ta", "corporate"],
                },
                {
                    "payment_method": "hotel_collectible",
                    "paid_to": "hotel",
                    "allowed_paid_by": ["hotel"],
                },
                {"payment_method": "UPI", "paid_to": "hotel"},
                {"payment_method": "treebo_points", "paid_to": "treebo"},
                {"payment_method": "razorpay_api", "paid_to": "treebo"},
                {"payment_method": "amazon_pay", "paid_to": "treebo"},
                {"payment_method": "bank_transfer_hotel", "paid_to": "hotel"},
                {"payment_method": "bank_transfer_treebo", "paid_to": "treebo"},
                {"payment_method": "tds_receivable_fy_24-25", "paid_to": "treebo"},
                {"payment_method": "cash", "paid_to": "hotel"},
                {"payment_method": "debit_card", "paid_to": "hotel"},
                {"payment_method": "credit_card", "paid_to": "hotel"},
                {"payment_method": "paid_at_ota", "paid_to": "treebo"},
                {"payment_method": "phone_pe", "paid_to": "treebo"},
                {"payment_method": "other", "paid_to": "hotel"},
                {"payment_method": "air_pay", "paid_to": "treebo"},
                {"payment_method": "payment_service", "paid_to": "treebo"},
                {"payment_method": "payment_link", "paid_to": "treebo"},
                {"payment_method": "razorpay_payment_gateway", "paid_to": "treebo"},
                {"payment_method": "credit_shell", "paid_to": "treebo"},
                {"payment_method": "transferred_credit", "paid_to": "treebo"},
                {"payment_method": "treebo_expense", "paid_to": "treebo"},
            ],
            "refund": [
                {"payment_method": "razorpay_payment_gateway", "paid_to": "treebo"},
                {"payment_method": "payment_service", "paid_to": "treebo"},
                {
                    "payment_method": "paid_by_treebo",
                    "paid_to": "treebo",
                    "allowed_paid_by": ["treebo"],
                },
                {"payment_method": "razorpay_api", "paid_to": "treebo"},
                {"payment_method": "amazon_pay", "paid_to": "treebo"},
                {"payment_method": "bank_transfer_hotel", "paid_to": "hotel"},
                {"payment_method": "cash", "paid_to": "hotel"},
                {"payment_method": "debit_card", "paid_to": "hotel"},
                {"payment_method": "credit_card", "paid_to": "hotel"},
                {"payment_method": "paid_at_ota", "paid_to": "treebo"},
                {"payment_method": "phone_pe", "paid_to": "treebo"},
                {"payment_method": "UPI", "paid_to": "hotel"},
                {"payment_method": "other", "paid_to": "hotel"},
                {"payment_method": "air_pay", "paid_to": "treebo"},
                {"payment_method": "credit_shell", "paid_to": "treebo"},
                {"payment_method": "treebo_points", "paid_to": "treebo"},
                {"payment_method": "treebo_corporate_rewards", "paid_to": "treebo"},
                {"payment_method": "transferred_credit", "paid_to": "treebo"},
                {"payment_method": "payout_link", "paid_to": "treebo"},
                {"payment_method": "treebo_expense", "paid_to": "treebo"},
                {
                    "payment_method": "settlement_against_payment_transfer",
                    "paid_to": "treebo",
                },
                {
                    "payment_method": "city_ledger",
                    "paid_to": "hotel",
                    "allowed_paid_by": ["hotel"],
                },
                {"payment_method": "bills_on_hold", "paid_to": "hotel"},
            ],
        }

    def mocked_units_credit_pay_modes(self, hotel_id):
        return [CITY_LEDGER]

    def mocked_refund_exclusion_list_for_backoffice(self, hotel_id):
        return []

    def mocked_get_bo_fallback_account_number(self, hotel_id):
        return "default"

    @staticmethod
    def mock_privilege_check(hotel_id):
        return True

    def mocked_generate_and_send_error_logs(
        self, date, hotel_id, ledger_controls, folder_path, hotel_code
    ):
        return

    DailyLedgerCreationCommandHandler.generate_gl_ledger_file = (
        mocked_generate_gl_ledger_file
    )
    DailyLedgerCreationCommandHandler.generate_cl_ledger_file = (
        mocked_generate_cl_ledger_file
    )
    DailyLedgerCreationCommandHandler.archive_ledger_files = mocked_archive_ledger_files
    DailyLedgerCreationCommandHandler.generate_and_send_error_logs = (
        mocked_generate_and_send_error_logs
    )
    TenantSettings.get_receive_later_payment_modes = mocked_receive_later_payment_modes
    TenantSettings.get_payment_debtor_mapping = mocked_payment_debtor_mapping
    TenantSettings.get_payment_config = mocked_payment_config
    LedgersFileView._do_privilege_check = mock_privilege_check
    TenantSettings.get_units_credit_pay_modes = mocked_units_credit_pay_modes
    TenantSettings.get_enabled_backoffice_erps = mocked_get_enabled_backoffice_erps
    TenantSettings.get_refund_exclusion_list_for_backoffice = (
        mocked_refund_exclusion_list_for_backoffice
    )
    TenantSettings.get_bo_fallback_account_number = (
        mocked_get_bo_fallback_account_number
    )
    TenantSettings.get_backoffice_hotel_code = mocked_get_backoffice_hotel_code
    yield
    DailyLedgerCreationCommandHandler.generate_gl_ledger_file = actual_gl_gen
    DailyLedgerCreationCommandHandler.generate_cl_ledger_file = actual_cl_gen
    DailyLedgerCreationCommandHandler.archive_ledger_files = actual_zip_gen
    DailyLedgerCreationCommandHandler.generate_and_send_error_logs = actual_err_logger
    TenantSettings.get_receive_later_payment_modes = actual_rec_later_payment
    TenantSettings.get_payment_debtor_mapping = actual_payment_debtor_mapping
    LedgersFileView._do_privilege_check = actual_prv_check
    TenantSettings.get_units_credit_pay_modes = actual_credit_payment_modes
    TenantSettings.get_enabled_backoffice_erps = actual_erps
    TenantSettings.get_refund_exclusion_list_for_backoffice = actual_exclusion_ref
    TenantSettings.get_bo_fallback_account_number = actual_bo_fallback_account_number
    TenantSettings.get_backoffice_hotel_code = actual_backoffice_hotel_code


def test_generate_backoffice_ledger_base_case(
    client,
    setup_base_raw_data,
    setup_tx_master,
    gl_item_repo,
    cl_item_repo,
):
    payload = dict(date=date_to_ymd_str(DATE))
    with mocked_context():
        response = generate_backoffice_ledgers(client, payload, hotel_id=HOTEL_ID)
        ledgers = response["data"]
    assert len(ledgers) == 3
    gl_controls = gl_item_repo.fetch(HOTEL_ID, DATE)
    assert len(gl_controls) == 33
    cl_controls = cl_item_repo.fetch(HOTEL_ID, DATE)
    assert len(cl_controls) == 15


def test_generate_backoffice_ledger_with_advance_transfer_on_invoicing(
    client,
    setup_base_raw_data,
    setup_tx_master,
    setup_checkout_data,
    gl_item_repo,
    cl_item_repo,
):
    payload = dict(date=date_to_ymd_str(CHECKOUT_FOR_ADVANCE))
    with mocked_context():
        response = generate_backoffice_ledgers(client, payload, hotel_id=HOTEL_ID)
        ledgers = response["data"]
    assert len(ledgers) == 3
    gl_controls = gl_item_repo.fetch(HOTEL_ID, CHECKOUT_FOR_ADVANCE)
    assert len(gl_controls) == 2
    cl_controls = cl_item_repo.fetch(HOTEL_ID, CHECKOUT_FOR_ADVANCE)
    assert len(cl_controls) == 4


def test_generate_backoffice_ledger_with_reversals_on_reverse_invoice_reissue(
    client,
    setup_base_raw_data,
    setup_tx_master,
    setup_reverse_checkout_data,
    gl_item_repo,
    cl_item_repo,
):
    payload = dict(
        date=date_to_ymd_str(POSTING_DATE_TO_TEST_REVERSE_CHECKOUT),
        erp_name=IntegratedERPs.PROLOGIC,
    )
    with mocked_context():
        response = generate_backoffice_ledgers(client, payload, hotel_id=HOTEL_ID)
        ledgers = response["data"]
    assert len(ledgers) == 3
    gl_controls = gl_item_repo.fetch(HOTEL_ID, POSTING_DATE_TO_TEST_REVERSE_CHECKOUT)
    assert len(gl_controls) == 8
    cl_controls = cl_item_repo.fetch(HOTEL_ID, POSTING_DATE_TO_TEST_REVERSE_CHECKOUT)
    assert len(cl_controls) == 6
