from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.tests.mockers import (
    bulk_push_sales,
    create_sales,
    generate_sales_summary,
    get_sales,
    update_sales,
)
from finance_erp.tests.payload_generator import (
    bulk_push_sales_payload,
    create_sales_payload,
    create_sales_payload_in_bulk,
    get_sales_payload,
    update_sales_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_sales_api(client):
    create_sales(client, create_sales_payload())
    sales_details = get_sales(client, get_sales_payload())
    assert sales_details
    assert validate_fields_are_not_null(sales_details)


def test_update_sales_api(client):
    create_sales(client, create_sales_payload())
    update_sales(client, update_sales_payload())
    sales_details = get_sales(client, get_sales_payload())
    assert sales_details["verified"] is False


def test_sales_aggregates_gets_deleted_on_updating_aggregated_sales_entries(
    client, sale_invoice_repo, sale_invoice_summary_repo
):
    create_sales(client, create_sales_payload_in_bulk())
    generate_sales_summary(client, dict())
    initial_sales_details = sale_invoice_repo.get_by_ids(["REF1231"])
    update_sales(client, update_sales_payload(unique_ref_id="REF1231"))
    sales_details_after_update = sale_invoice_repo.get_by_ids(["REF1231"])
    assert sales_details_after_update[0].status == "ingested"
    assert sales_details_after_update[0].aggregation_id is None
    sales_summary = sale_invoice_summary_repo.get_by_ids(
        [initial_sales_details[0].aggregation_id]
    )
    assert sales_summary[0].deleted == True


def test_bulk_push_sales_api(client):
    create_sales(client, create_sales_payload_in_bulk())
    generate_sales_summary(client, dict())
    bulk_push_sales(client, dict(push_all=True))
    sales_details = get_sales(client, {"unique_ref_ids": "REF1231"})
    assert sales_details["status"] == SalesInvoiceStatus.PUSHED
