import datetime
import logging
from functools import wraps

from treebo_commons.multitenancy.sqlalchemy import db_engine

from finance_erp.common.globals import consumer_context

logger = logging.getLogger(__name__)


def timed(func):
    """This decorator prints the execution time for the decorated function."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start = datetime.datetime.now()
        result = func(*args, **kwargs)
        end = datetime.datetime.now()
        logger.info(
            "{} ran in {} msec".format(
                func.__name__, (end - start).total_seconds() * 1000
            )
        )
        return result

    return wrapper


def consumer_middleware(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        from flask import current_app

        with current_app.test_request_context():
            from flask import request

            from finance_erp.app import custom_after_request, custom_before_request

            request_headers = dict(request.headers)
            request_headers["X-Tenant-Id"] = consumer_context.tenant_id
            custom_before_request(request_headers)
            r_val = func(*args, **kwargs)
            custom_after_request(None, None)
            return r_val

    return wrapper


def session_manager(commit=False):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            session = db_engine.get_scoped_session()

            try:
                r_val = func(*args, **kwargs)
                if commit:
                    session.commit()
                return r_val
            except Exception as e:
                session.rollback()
                raise

            finally:
                session.remove()

        return wrapper

    return decorator
