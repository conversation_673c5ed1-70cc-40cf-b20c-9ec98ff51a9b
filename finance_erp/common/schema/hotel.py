from typing import Optional

from pydantic import BaseModel, Field

from finance_erp.common.constants import GSTType
from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class HotelPushSchema(BaseModel):
    vendor_name: Optional[str] = Field(default=None, alias="LegalName")
    search_name: Optional[str] = Field(default=None, alias="Name")
    address: Optional[str] = Field(default=None, alias="Address")
    city: Optional[str] = Field(default=None, alias="City")
    country_code: Optional[str] = Field(default=None, alias="CountryCode")
    pan: Optional[str] = Field(default=None, alias="PANNo")
    state_code: Optional[str] = Field(default=None, alias="State")
    gstin: Optional[str] = Field(default=None, alias="GSTRegistrationNo")
    gst_vendor_type: Optional[str] = Field(
        default=GSTType.REGISTERED.value, alias="GSTVendorType"
    )
    hotel_code: str = Field(alias="HotelCode")
    msme: Optional[str] = Field(default=None, alias="MSME")
    cost_center_id: Optional[str] = Field(default=None, alias="CostCenterID")

    class Config:
        validate_by_name = True
        from_attributes = True


class HotelPushRequestSchema(HotelPushSchema, BusinessCentralDataPushSchema):
    ...


class HotelUpdateSchema(BaseModel):
    hotel_code: Optional[str] = Field(default=None, alias="HotelCode")
    verified: Optional[bool] = None


class HotelResponseSchema(HotelPushSchema):
    status: str
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
