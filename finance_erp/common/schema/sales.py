from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.constants import GSTType
from finance_erp.common.schema.common import (
    BaseInvoicePushSchema,
    BusinessCentralDataPushSchema,
    NavisionResourceCommonResponseSchema,
)


class SalesInvoicePushSchema(BaseInvoicePushSchema):
    customer_number: Optional[str] = Field(None, alias="CustomerNo")
    gst_customer_type: Optional[str] = Field(
        GSTType.REGISTERED.value, alias="GSTCustomerType"
    )
    billed_to_state_code: Optional[str] = Field(None, alias="GSTBilltoStateCode")
    billed_to_gstin: Optional[str] = Field(None, alias="CustGSTRegNo")
    remarks: Optional[str] = Field(None, alias="Remarks")
    billed_to_legal_name: Optional[str] = Field(None, alias="CustomerName")
    hsn_code: Optional[str] = Field(None, alias="HSN_SACCode")
    original_invoice_number: Optional[str] = Field(None, alias="OriginalInvoiceNo")
    invoice_charge_type: Optional[str] = Field(None, alias="Type")
    booking_owner_legal_entity_id: Optional[str] = Field(
        None, alias="BookingOwnerLegalEntityId"
    )

    class Config:
        validate_by_name = True
        from_attributes = True


class SalesInvoiceIngestSchema(SalesInvoicePushSchema):
    tax_type: Optional[str] = Field(None, alias="TaxType")
    igst: Optional[float] = Field(None, alias="IGST")
    cgst: Optional[float] = Field(None, alias="CGST")
    sgst: Optional[float] = Field(None, alias="SGST")
    buy_side_invoice_number: Optional[str] = Field(None, alias="BuySideInvoiceNumber")

    class Config:
        validate_by_name = True
        from_attributes = True


class SalesInvoiceResponseSchema(
    SalesInvoiceIngestSchema, NavisionResourceCommonResponseSchema
):
    status: Optional[str] = None

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class SalesInvoiceSummarySchema(BaseModel):
    entry_type: Optional[str] = None
    order_date: Optional[date] = None
    posting_date: Optional[date] = None
    unit_price: Optional[float] = None
    tax_percentage: Optional[int] = None
    cgst: Optional[float] = None
    sgst: Optional[float] = None
    igst: Optional[float] = None
    uvid_date: Optional[date] = None
    unique_ref_id: Optional[str] = None
    hsn_code: Optional[str] = None
    cost_center_id: Optional[str] = None

    @field_validator("order_date", mode="before")
    def parse_order_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("uvid_date", mode="before")
    def parse_uvid_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    class Config:
        validate_by_name = True
        from_attributes = True


class SaleInvoiceSummaryPushRequestSchema(
    SalesInvoiceSummarySchema, BusinessCentralDataPushSchema
):
    ...


class SalesInvoiceUpdateSchema(BaseModel):
    unique_ref_id: Optional[str] = Field(default=None, alias="TransactionRefId")
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
