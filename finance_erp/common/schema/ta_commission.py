from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class TACommissionBaseSchema(BaseModel):
    posting_date: Optional[date] = Field(default=None, alias="PostingDate")
    commission_amount: float = Field(..., alias="CommissionAmount")
    pretax_room_rent: float = Field(..., alias="NBV")
    reference_number: str = Field(..., alias="BookingID")
    hotel_code: str = Field(..., alias="Hotelcode")
    check_in: Optional[date] = Field(default=None, alias="CheckInDate")
    check_out: Optional[date] = Field(default=None, alias="CheckOutDate")
    guest_name: str = Field(..., alias="GuestName")
    ta_name: Optional[str] = Field(None, alias="TAName")
    ta_sh_profile_code: Optional[str] = Field(None, alias="TASHProfileCode")
    commission_percent: float = Field(..., alias="CommissionPer")
    booking_created_date: Optional[date] = Field(
        default=None, alias="BookingCreationDate"
    )
    tds_percentage: str = Field(..., alias="TDSPer")
    mop: str = Field(..., alias="MOP")
    paid_at_ota: Optional[float] = Field(None, alias="GBV")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("commission_amount", mode="before")
    def parse_commission_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("pretax_room_rent", mode="before")
    def parse_pretax_room_rent(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("check_in", mode="before")
    def parse_check_in(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("check_out", mode="before")
    def parse_check_out(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("commission_percent", mode="before")
    def parse_commission_percent(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("booking_created_date", mode="before")
    def parse_booking_created_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("paid_at_ota", mode="before")
    def parse_paid_at_ota(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class TACommissionPushSchema(TACommissionBaseSchema):
    uu_id: str = Field(..., alias="UUID")
    entry_type: str = Field(..., alias="EntryType")
    commission_amount: float = Field(..., alias="CommissionAmount")
    pretax_room_rent: float = Field(..., alias="NBV")
    cost_center_id: Optional[str] = Field(None, alias="CostCenterID")

    class Config:
        validate_by_name = True
        from_attributes = True


class TACommissionPushRequestSchema(
    TACommissionPushSchema, BusinessCentralDataPushSchema
):
    ...


class TACommissionResponseSchema(TACommissionPushSchema):
    status: Optional[str] = None
    verified: Optional[bool] = None
    commission_amount: Optional[float] = None
    pretax_room_rent: Optional[float] = None

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class TACommissionUpdateSchema(BaseModel):
    uu_id: str = Field(default=None, alias="UUID")
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
