from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import (
    date_to_ymd_str,
    isoformat_datetime,
    ymd_str_to_date,
)

from finance_erp.application.common.utils import sanitize_string
from finance_erp.common.constants import (
    GSTType,
    PurchaseInvoiceReportCategory,
    PurchaseInvoiceTypes,
)
from finance_erp.common.schema.common import (
    BaseInvoicePushSchema,
    BusinessCentralDataPushSchema,
    NavisionResourceCommonResponseSchema,
)


class PurchaseInvoicePushSchema(BaseInvoicePushSchema):
    vendor_number: Optional[str] = Field(None, alias="VendorNo")
    due_date: Optional[date] = Field(None, alias="DueDate")
    gst_vendor_type: Optional[str] = Field(
        default=GSTType.REGISTERED.value, alias="GSTVendorType"
    )

    remark: Optional[str] = Field(None, alias="Remark")
    hsn_code: Optional[str] = Field(None, alias="HSNSACCode")
    original_invoice_number: Optional[str] = Field(None, alias="OriginalInvNo")
    cost_center_id: Optional[str] = Field(None, alias="CostCenterID")
    purchase_type: Optional[str] = Field(
        default=PurchaseInvoiceTypes.RESELLER_INVOICE.value, alias="PurchaseType"
    )

    @field_validator("due_date", mode="before")
    def parse_due_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("remark", mode="before")
    def parse_remark(cls, value):
        if isinstance(value, str):
            return sanitize_string(value, 250)
        return value

    class Config:
        validate_by_name = True
        from_attributes = True


class PurchaseInvoiceIngestSchema(PurchaseInvoicePushSchema):
    source_created_on: Optional[date] = Field(None, alias="SourceCreatedOn")
    customer_invoice_number: Optional[str] = Field(None, alias="CustomerInvoiceNumber")
    report_category: Optional[str] = Field(
        default=PurchaseInvoiceReportCategory.RESELLER_PURCHASE_INVOICE_REPORT.value,
        alias="ReportCategory",
    )

    @field_validator("source_created_on", mode="before")
    def parse_source_created_on(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class PurchaseInvoiceResponseSchema(
    PurchaseInvoiceIngestSchema, NavisionResourceCommonResponseSchema
):
    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if isinstance(value, datetime):
                data[field] = isoformat_datetime(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class PurchaseInvoicePushRequestSchema(
    PurchaseInvoicePushSchema, BusinessCentralDataPushSchema
):
    ...


class PurchaseInvoiceUpdateSchema(BaseModel):
    unique_ref_id: Optional[str] = Field(default=None, alias="TransactionRefId")
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
