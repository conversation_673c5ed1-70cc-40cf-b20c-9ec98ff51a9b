from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel, field_validator


class LedgersFileResponseSchema(BaseModel):
    hotel_id: str
    business_date: date
    ledger_file_type: Optional[str] = None
    ledger_file_path: Optional[str] = None
    ledger_file_name: Optional[str] = None
    status: Optional[str] = None
    remarks: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    id: Optional[str] = None

    @field_validator("id", mode="before")
    def parse_id(cls, v):
        if isinstance(v, int):
            return str(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True
