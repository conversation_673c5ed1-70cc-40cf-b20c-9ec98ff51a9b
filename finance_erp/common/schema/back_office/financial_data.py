from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date


class BaseFinancialSchema(BaseModel):
    uu_id: Optional[str] = None
    fin_erp_posting_date: Optional[date] = None
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("fin_erp_posting_date", mode="before")
    def parse_fin_erp_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v


class BaseRequestSchema(BaseFinancialSchema):
    bill_id: str
    hotel_id: str
    booking_id: Optional[str] = None
    category: Optional[str] = None
    owner_name: Optional[str] = None
    folio_number: Optional[str] = None
    checkin_date: Optional[datetime] = None
    checkout_date: Optional[datetime] = None
    billed_entity_id: Optional[str] = None
    account_number: Optional[str] = None
    revenue_center: Optional[str] = None
    booking_reference_number: Optional[str] = None

    @field_validator("checkin_date", mode="before")
    def parse_checkin_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("checkout_date", mode="before")
    def parse_checkout_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class TaxDetailSchema(BaseModel):
    amount: float
    percentage: float
    tax_type: str

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("percentage", mode="before")
    def parse_percentage(cls, v):
        if isinstance(v, str):
            return float(v)
        return v


class PaymentSplitDetailSchema(BaseModel):
    amount: float
    payment_id: int
    payment_split_id: int

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v


class PaymentRequestSchema(BaseRequestSchema):
    payment_id: Optional[str] = None
    payment_split_id: Optional[str] = None
    payment_type: Optional[str] = None
    amount: float
    posting_date: Optional[date] = None
    date_of_payment: Optional[datetime] = None
    payment_mode: Optional[str] = None
    crs_payment_mode: Optional[str] = None
    crs_payment_mode_sub_type: Optional[str] = None
    debtor_code: Optional[str] = None
    payment_mode_sub_type: Optional[str] = None
    payment_ref_id: Optional[str] = None
    payment_channel: Optional[str] = None
    hotel_id: str
    room_number: Optional[str] = None

    @field_validator("date_of_payment")
    def parse_date_of_payment(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v


class ChargeRequestSchema(BaseRequestSchema):
    charge_id: str
    charge_split_id: str
    pretax_amount: float
    posttax_amount: float
    tax_amount: float
    tax_details: List[TaxDetailSchema]
    charge_type: Optional[str] = None
    posting_date: Optional[date] = None
    applicable_business_date: Optional[date] = None
    bill_to_type: Optional[str] = None
    sku_category_id: Optional[str] = None
    is_inclusion_charge: Optional[bool] = None
    item_id: Optional[str] = None

    @field_validator("pretax_amount", mode="before")
    def parse_pretax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("posttax_amount", mode="before")
    def parse_posttax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("applicable_business_date", mode="before")
    def parse_applicable_business_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v


class AllowanceRequestSchema(BaseRequestSchema):
    allowance_id: str
    charge_id: str
    charge_split_id: str
    posting_date: str
    tax_amount: float
    posttax_amount: float
    pretax_amount: float
    tax_details: List[TaxDetailSchema]
    charge_type: Optional[str] = None
    bill_to_type: Optional[str] = None
    item_id: Optional[str] = None
    sku_category_id: Optional[str] = None

    @field_validator("tax_amount", mode="before")
    def parse_tax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("pretax_amount", mode="before")
    def parse_pretax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("posttax_amount", mode="before")
    def parse_posttax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v


class FolioDetailsRequestSchema(BaseRequestSchema):
    first_name: Optional[str] = None
    is_credit_folio: Optional[bool] = None
    folio_status: Optional[str] = None
    payment_split_details: Optional[List[PaymentSplitDetailSchema]] = None


class FinancialDataSchema(BaseModel):
    hotel_id: str
    date: str
    payment_details: Optional[List[PaymentRequestSchema]] = None
    charge_details: Optional[List[ChargeRequestSchema]] = None
    allowance_details: Optional[List[AllowanceRequestSchema]] = None
    folio_details: Optional[List[FolioDetailsRequestSchema]] = None
