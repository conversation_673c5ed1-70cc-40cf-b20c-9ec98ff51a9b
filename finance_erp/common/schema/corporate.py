from datetime import date
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.common.constants import GSTType
from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class CorporateBillSchedulingRequest(BaseModel):
    corporate_code: Optional[str] = None
    billing_date: Optional[date] = None

    @field_validator("billing_date", mode="before")
    def parse_billing_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class CorporatePushSchema(BaseModel):
    customer_legal_name: Optional[str] = Field(default=None, alias="LegalName")
    customer_trading_name: Optional[str] = Field(default=None, alias="TradingName")
    address: Optional[str] = Field(default=None, alias="Address")
    city: Optional[str] = Field(default=None, alias="City")
    phone_number: Optional[str] = Field(default=None, alias="PhoneNumber")
    post_code: Optional[str] = Field(default=None, alias="PostCode")
    email: Optional[str] = Field(default=None, alias="Email")
    credit_limit: Optional[str] = Field(default=None, alias="CreditLimit")
    country_code: Optional[str] = Field(default=None, alias="CountryCode")
    pan: Optional[str] = Field(default=None, alias="PANNo")
    state_code: Optional[str] = Field(default=None, alias="State")
    gstin: Optional[str] = Field(default=None, alias="GSTRegistrationNo")
    gst_customer_type: Optional[str] = Field(
        default=GSTType.REGISTERED.value, alias="GSTCustomerType"
    )
    corporate_code: str = Field(alias="AthenaCode")
    tan_number: Optional[str] = Field(default=None, alias="TANNo")

    class Config:
        validate_by_name = True
        from_attributes = True


class CorporatePushRequestSchema(CorporatePushSchema, BusinessCentralDataPushSchema):
    ...


class CorporateResponseSchema(CorporatePushSchema):
    status: str
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True


class CorporateUpdateSchema(BaseModel):
    corporate_code: str = Field(alias="CorporateCode")
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
