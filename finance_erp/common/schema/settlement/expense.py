from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class ExpensePushSchema(BaseModel):
    posting_date: date = Field(..., alias="PostingDate")
    pretax_amount: Optional[float] = Field(default=0.0, alias="PTaxAmount")
    tds_per: Optional[float] = Field(default=None, alias="TDSPer")
    hotel_code: str = Field(..., alias="HotelCode")
    invoice_number: Optional[str] = Field(default=None, alias="VendorInvoiceNo")
    invoice_amount: Optional[float] = Field(default=None, alias="InvoiceAmount")
    invoice_date: Optional[date] = Field(default=None, alias="InvoiceDate")
    entry_type: str = Field(..., alias="EntryType")
    hsn_code: str = Field(..., alias="HSNSACCode")
    doc_type: str = Field(..., alias="DocType")
    remarks: str = Field(..., alias="Remarks")
    cost_center_id: Optional[str] = Field(default=None, alias="CostCenterID")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("invoice_date", mode="before")
    def parse_invoice_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("pretax_amount", mode="before")
    def parse_pretax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("tds_per", mode="before")
    def parse_tds_per(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("invoice_amount", mode="before")
    def parse_invoice_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class ExpenseResponseSchema(ExpensePushSchema):
    posted_on: Optional[date] = Field(default=None, alias="PostedOn")
    status: str = Field(...)
    verified: bool = Field(...)
    uu_id: str = Field(...)

    @field_validator("posted_on", mode="before")
    def parse_posted_on(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class ExpensePushRequestSchema(ExpensePushSchema, BusinessCentralDataPushSchema):
    ...


class ExpenseUpdateSchema(BaseModel):
    uu_id: str
    verified: Optional[bool] = None
