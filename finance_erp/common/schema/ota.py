from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class OTACommissionPushSchema(BaseModel):
    posting_date: Optional[date] = Field(default=None, alias="PostingDate")
    paid_at_ota: Optional[float] = Field(default=None, alias="GBV")
    commission_amount: Optional[float] = Field(default=None, alias="CommissionAmount")
    tds_percentage: Optional[str] = Field(default=None, alias="TDSPer")
    reference_number: str = Field(default=None, alias="BookingID")
    hotel_code: Optional[str] = Field(default=None, alias="Hotelcode")
    check_in: Optional[date] = Field(default=None, alias="CheckInDate")
    check_out: Optional[date] = Field(default=None, alias="CheckOutDate")
    pretax_room_rent: Optional[float] = Field(default=None, alias="NBV")
    guest_name: Optional[str] = Field(default=None, alias="GuestName")
    ota_name: Optional[str] = Field(default=None, alias="OTAName")
    commission_percent: Optional[float] = Field(default=None, alias="CommissionPer")
    mop: Optional[str] = Field(default=None, alias="MOP")
    booking_created_date: Optional[date] = Field(
        default=None, alias="BookingCreationDate"
    )
    cost_center_id: Optional[str] = Field(default=None, alias="CostCenterID")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("paid_at_ota", mode="before")
    def parse_paid_at_ota(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("commission_amount", mode="before")
    def parse_commission_amount(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("check_in", mode="before")
    def parse_check_in(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("check_out", mode="before")
    def parse_check_out(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("pretax_room_rent", mode="before")
    def parse_pretax_room_rent(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("commission_percent", mode="before")
    def parse_commission_percent(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("booking_created_date", mode="before")
    def parse_booking_created_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    class Config:
        validate_by_name = True
        from_attributes = True


class OTACommissionPushRequestSchema(
    OTACommissionPushSchema, BusinessCentralDataPushSchema
):
    ...


class OTACommissionResponseSchema(OTACommissionPushSchema):
    booking_on: Optional[str] = Field(default=None, alias="BookingOn")
    status: Optional[str] = None
    verified: Optional[bool] = None

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class OTACommissionUpdateSchema(BaseModel):
    reference_number: Optional[str] = Field(alias="BookingID", default=None)
    verified: Optional[bool] = None

    class Config:
        validate_by_name = True
        from_attributes = True
