from datetime import date
from typing import Optional

from pydantic import (
    BaseModel,
    Field,
    field_serializer,
    field_validator,
    model_validator,
)
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.application.common.utils import sanitize_string
from finance_erp.common.constants import AR_MODULE, PHONE_PE_PAY_MODE, RAZORPAY_PAY_MODE
from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class BookingOwnerSchema(BaseModel):
    name: str
    phone: Optional[str] = None
    email: Optional[str] = None


class BasePGTransactionPushSchema(BaseModel):
    pg_charges: Optional[float] = Field(default=None, alias="PGCharges")
    pg_tax: Optional[float] = Field(default=None, alias="PGTaxes")
    platform_fees: Optional[float] = Field(default=None, alias="PlatformFees")
    pg_transaction_id: Optional[str] = Field(
        default=None, alias="PaymentGatewayTransactionId"
    )
    reference_number: Optional[str] = Field(default=None, alias="BookingID")
    hotel_code: Optional[str] = Field(default=None, alias="HotelCode")
    paid_by: Optional[str] = Field(default=None, alias="PaidBy")
    paid_to: Optional[str] = Field(default=None, alias="PaidTo")
    payment_type: Optional[str] = Field(default=None, alias="PaymentType")
    payment_amount: Optional[float] = Field(default=None, alias="PaymentAmount")
    paymode: Optional[str] = Field(default=None, alias="Paymode")
    paymode_type: Optional[str] = Field(default=None, alias="PaymodeType")
    payor_entity: Optional[str] = Field(default=None, alias="PayorEntity")
    athena_code: Optional[str] = Field(default=None, alias="AthenaCode")
    payor_name: Optional[str] = Field(default=None, alias="PayorName")
    hotel_name: Optional[str] = Field(default=None, alias="HotelName")
    invoice_id: Optional[str] = Field(default=None, alias="InvoiceId")
    channel: Optional[str] = Field(default=None, alias="Channel")
    sub_channel: Optional[str] = Field(default=None, alias="SubChannel")
    original_booking_amount: Optional[float] = Field(
        default=None, alias="OriginalBookingAmount"
    )
    is_advance: bool = Field(default=False, alias="IsAdvance")
    refund_reason: Optional[str] = Field(default=None, alias="RefundReason")

    @field_validator("pg_charges", mode="before")
    def parse_pg_charges(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("pg_tax", mode="before")
    def parse_pg_tax(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("platform_fees", mode="before")
    def parse_platform_fees(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("payment_amount", mode="before")
    def parse_payment_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("original_booking_amount", mode="before")
    def parse_original_booking_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class PGTransactionPushSchema(BasePGTransactionPushSchema):
    posting_date: Optional[date] = Field(default=None, alias="PostingDate")
    payment_date: Optional[date] = Field(default=None, alias="PaymentDate")
    booker_entity: Optional[str] = Field(default=None, alias="BookerEntity")
    booking_owner: Optional[BookingOwnerSchema] = Field(
        default=None, alias="BookingOwner"
    )
    check_in: Optional[date] = Field(default=None, alias="CheckInDate")
    check_out: Optional[date] = Field(default=None, alias="CheckOutDate")
    seller_model: Optional[str] = Field(default=None, alias="SellerModel")
    uu_id: str = Field(..., alias="UUID")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("payment_date", mode="before")
    def parse_payment_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("check_in", mode="before")
    def parse_check_in(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("check_out", mode="before")
    def parse_check_out(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_serializer("booking_owner")
    def serialize_booking_owner(self, owner: Optional[BookingOwnerSchema], _info):
        if owner is None:
            return None
        return owner.model_dump()

    class Config:
        validate_by_name = True
        from_attributes = True


class PGTransactionNAVPushSchema(BasePGTransactionPushSchema):
    posting_date: Optional[date] = Field(default=None, alias="PostingDate")
    payment_date: Optional[date] = Field(default=None, alias="PaymentDate")
    pg_name: Optional[str] = Field(default=None, alias="PGType")
    amount: Optional[float] = Field(default=None, alias="Amount")
    name: Optional[str] = Field(default=None, alias="ContactName")
    phone: Optional[str] = Field(default=None, alias="ContactPhoneNo")
    email: Optional[str] = Field(default=None, alias="ContactEmail")
    check_in: Optional[date] = Field(default=None, alias="CheckInDate")
    check_out: Optional[date] = Field(default=None, alias="CheckOutDate")
    cost_center_id: Optional[str] = Field(default=None, alias="CostCenterID")

    @model_validator(mode="before")
    def preprocess_fields(cls, values):
        values["SubChannel"] = sanitize_string(values.get("sub_channel"), 30)
        values["PayorEntity"] = sanitize_string(values.get("payor_entity"), 20)
        values["BookerEntity"] = sanitize_string(values.get("booker_entity"), 20)
        values["AthenaCode"] = sanitize_string(values.get("athena_code"), 20)
        values["PayorName"] = sanitize_string(values.get("payor_name"), 80)
        values["Amount"] = values.get("payment_amount")

        booking_owner = values.get("booking_owner")
        if booking_owner:
            values["ContactName"] = booking_owner.get("name")
            values["ContactPhoneNo"] = booking_owner.get("phone")
            values["ContactEmail"] = booking_owner.get("email")

        paymode = values.get("paymode")
        if paymode == RAZORPAY_PAY_MODE:
            values["PGType"] = "razorpay"
        elif paymode == PHONE_PE_PAY_MODE:
            values["PGType"] = "phonepe"

        if values.get("channel") == AR_MODULE:
            values["PaymentGatewayTransactionId"] = values.get("uu_id")

        return values

    class Config:
        validate_by_name = True
        from_attributes = True


class PGTransactionPushRequestSchema(
    PGTransactionNAVPushSchema, BusinessCentralDataPushSchema
):
    ...


class PGTransactionResponseSchema(PGTransactionPushSchema):
    payment_on: Optional[str] = Field(default=None, alias="PaymentOn")
    status: Optional[str] = None
    verified: Optional[bool] = None
    uu_id: Optional[str] = None
    aggregation_id: Optional[str] = None

    class Config:
        validate_by_name = True
        populate_by_name = True
        from_attributes = True


class PGTransactionUpdateSchema(BaseModel):
    uu_id: str
    verified: Optional[bool] = None
