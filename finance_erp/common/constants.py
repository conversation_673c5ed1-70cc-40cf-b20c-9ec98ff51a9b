import re
from datetime import date

from ths_common.constants.base_enum import BaseEnum


class NavisionReports(object):
    CUSTOMER_INVOICE_REPORT = "customer_invoice_report"
    CUSTOMER_INVOICE_SUMMARY_REPORT = "customer_invoice_summary_report"
    PURCHASE_INVOICE_REPORT = "purchase_invoice_report"
    PAYMENT_GATEWAY_REPORT = "payment_gateway_report"
    PAYMENT_GATEWAY_SUMMARY_REPORT = "payment_gateway_summary_report"
    HOTEL_REPORT = "hotel_report"
    CORPORATE_REPORT = "corporate_report"
    OTA_COMMISSION_REPORT = "ota_commission_report"
    TREEBO_FEE_REPORT = "settlement_treebo_fee_report"
    EXPENSE_REPORT = "settlement_expense_report"
    HOTEL_ADJUSTMENT_REPORT = "settlement_hotel_adjustment_report"
    LOAN_REPORT = "settlement_loan_report"
    TAX_REPORT = "settlement_tax_report"
    TA_COMMISSION_REPORT = "ta_commission_report"
    AR_PAYMENT_REPORT = "ar_payment_report"


class PurchaseInvoiceReportCategory(BaseEnum):
    MARKETPLACE_PURCHASE_INVOICE_REPORT = "marketplace_purchase_invoice_report"
    RESELLER_PURCHASE_INVOICE_REPORT = "reseller_purchase_invoice_report"


class PurchaseInvoiceTypes(BaseEnum):
    FUNDING_INVOICE = "Funding Invoice"
    TREEBO_STAY_INVOICE = "Treebo Stay Invoice"
    RESELLER_INVOICE = "Purchase invoice of reseller"


class EntityTypes(object):
    POS_REVENUE = "POS_REVENUE"


class ProcessNames(object):
    INGESTION = "ingestion"
    NAVISION_PUSH = "navision_push"
    DATA_VERIFICATION = "navision_push"
    PULL = "pull"


class DataPushNavKeys(object):
    PURCHASE = "json_purchase"
    PAYMENTS = "json_paymentgatewayNew"
    SALES = "json_sales"
    HOTEL = "json_hotel"
    CORPORATE = "json_corporate"
    TREEBO_FEE = "json_treebofee"
    EXPENSE = "json_expenseentry"
    HOTEL_ADJUSTMENT = "json_hoteladjust"
    LOAN = "json_loanentry"
    TAX = "json_taxentry"
    OTA = "json_otacommission"
    TA = "json_tacommission"
    REQUEST_OUTER_WRAPPER_KEY = "jsonText"


class InvoiceEntryType(BaseEnum):
    ORDER = "order"
    CREDIT = "credit memo"


class Structure(BaseEnum):
    GST = "GST"


class NatureOfSupply(BaseEnum):
    B2B = "B2B"
    B2C = "B2C"


class GSTType(BaseEnum):
    REGISTERED = "Registered"
    UNREGISTERED = "Unregistered"


class TACommissionEntryType(BaseEnum):
    DEBIT = "debit"
    CREDIT = "credit"


class CountryCode(BaseEnum):
    INDIA = "IND"


class HotelAdjustmentTypes(object):
    GST_DEBIT = "GSTDebit"


# This guard is added to block old invoices dispatch/summary which ingested to fin erp on reissue
FIN_ERP_MINT_MIGRATION_GUARD_DATE = date(year=2024, month=10, day=27)
ADMIN_NAME = "Finance Erp by Treebo"
ADMIN_TEMPLATE = "bootstrap3"
BTT_PAY_MODE = "paid_by_treebo"
BTT_CHANNEL = "treebo-internal"
AR_MODULE = "ar_module"
PAYMENT_DATA_PUSH_CONFIG = "nav_payment_data_push_config"
FINANCE_ERP_METABASE_HOME_DASHBOARD_ID = "finance_erp.metabase_home_dashboard_id"
BUSINESS_CENTRAL_ENDPOINT_CONFIG = "business_central_endpoint_config"
RAZORPAY_PAY_MODE = "razorpay_api"
PHONE_PE_PAY_MODE = "phone_pe"
TRANSACTION_MASTER_SUB_CODE_DETAILS = "transaction_master_sub_code_details"


class IngestionJobs(object):
    INVOICE_INGESTION = "invoice_ingestion"
    CREDIT_NOTE_INGESTION = "credit_note_ingestion"
    BOOKING_INGESTION = "booking_ingestion"
    CP_CORPORATE_INGESTION = "corporate_ingestion"
    LEDGERS_FILE_INGESTION = "ledgers_file_ingestion"
    TRANSACTION_MASTER_DATA_INGESTION = "transaction_master_data_ingestion"
    BACKOFFICE_FINANCIAL_DATA_INGESTION = "backoffice_financial_data_ingestion"


class InvoiceTypes(object):
    NON_CREDIT = "non-credit"
    SPOT_CREDIT = "spot-credit"
    NON_SPOT_CREDIT = "non-spot-credit"

    @classmethod
    def is_credit(cls, value):
        return value in (InvoiceTypes.NON_SPOT_CREDIT, InvoiceTypes.SPOT_CREDIT)

    @classmethod
    def all(cls):
        return [value for key, value in vars(cls).items() if not key.startswith("__")]


class LedgersFileStatus:
    CREATED = "created"
    FAILED = "failed"
    SUCCESS = "success"


class LedgersFileType:
    CITY_LEDGER_FILE = "city_ledger_file"
    GUEST_LEDGER_FILE = "guest_ledger_file"
    CONSOLIDATED_LEDGER_ARCHIVE = "consolidated_ledger_archive"

    @classmethod
    def all(cls):
        return [value for key, value in vars(cls).items() if not key.startswith("__")]


CORPORATE_BILLING_OFFSET = 2
EMAIL_REGEX = re.compile(r"[^@]+@[^@]+\.[^@]+")
MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED = 200
