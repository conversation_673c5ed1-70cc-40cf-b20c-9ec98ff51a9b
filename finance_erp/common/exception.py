from ths_common.exceptions import AuthorizationError


class NavisionDuplicateException(Exception):
    pass


class DefinitiveFailureException(Exception):
    def __init__(
        self,
        message,
        can_retry_once=None,
        retry_after_hours=24,
        send_alert_after_retry_threshold=True,
    ):
        self.can_retry_once = can_retry_once
        self.retry_after_hours = retry_after_hours
        self.send_alert_after_retry_threshold = send_alert_after_retry_threshold
        super().__init__(message)


class PolicyAuthException(AuthorizationError):
    error_code = "0017"
    message = "You're not authorized to perform this operation"

    def __init__(self, error=None, message=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(PolicyAuthException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DataAlreadyPushedException(Exception):
    def __init__(self, message="Already Pushed Records can not be updated"):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class ARServiceException(Exception):
    def __init__(self, message="AR Service Exception"):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class CRSServiceException(Exception):
    def __init__(self, message="CRS Service Exception"):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class ResellerServiceException(Exception):
    def __init__(self, message="Reseller Service Exception"):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class PaymentAggregationException(Exception):
    def __init__(self, message="Error occurred while aggregating payment data."):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class PurchaseDataUpdateException(Exception):
    def __init__(self, message="Error occurred while updating purchase data."):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class PaymentDataUpdateException(Exception):
    def __init__(self, message="Error occurred while updating payment data."):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class SalesDataUpdateException(Exception):
    def __init__(self, message="Error occurred while updating sales data."):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message


class SalesDataAggregationException(Exception):
    def __init__(self, message="Error occurred while aggregating sales data."):
        super().__init__(message)
        self.message = message

    def __str__(self):
        return self.message
