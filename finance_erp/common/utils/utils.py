import uuid
from collections import defaultdict
from datetime import datetime

from ths_common.utils.id_generator_utils import random_id_generator


def group_by_attribute(object_list, attribute_name):
    default_list = defaultdict(list)
    for _object in object_list:
        key = getattr(_object, attribute_name)
        default_list[key].append(_object)
    return default_list


def dmy_str_to_ymd_str(date):
    return datetime.strptime(date, "%d-%m-%Y").strftime("%Y-%m-%d")


def fin_erp_random_id_generator(prefix=None, max_length=None):
    if max_length is None:
        return random_id_generator(prefix=prefix)
    created_at = datetime.utcnow()
    part1 = created_at.strftime("%d%m%y")
    prefix = f"{prefix or ''}-{part1}-"
    if max_length < len(prefix):
        raise Exception("Unable to generate UUID")
    allowed_length_for_random_id = max_length - len(prefix)
    random_part = str(uuid.uuid4().int)[:allowed_length_for_random_id]
    return f"{prefix}{random_part}"


def partition(input_list, predicate):
    true = []
    false = []
    for item in input_list:
        if predicate(item):
            true.append(item)
        else:
            false.append(item)
    return true, false


def are_lists_equal(list1, list2):
    if len(list1) != len(list2):
        return False

    return all(a == b for a, b in zip(list1, list2))
