# coding=utf-8
"""
Base Repository
"""
import abc
import logging

import sqlalchemy
from sqlalchemy import not_
from sqlalchemy.orm import lazyload
from sqlalchemy.orm.exc import FlushError, MultipleResultsFound, NoResultFound
from ths_common.exceptions import (
    BookingReferenceIdCollision,
    DatabaseError,
    DatabaseLockError,
    PrimaryKeyCollision,
    UniqueIdCollision,
    ValidationException,
)
from treebo_commons.flask_audit.plugin import annotate_changes
from treebo_commons.multitenancy.sqlalchemy import db_engine

logger = logging.getLogger(__name__)


def handle_integrity_error(exception):
    # TODO There could be other integrity errors. Check for only collisions
    if (
        "reference_number".upper() in exception.__str__().upper()
        and "DUPLICATE KEY VALUE VIOLATES UNIQUE CONSTRAINT".upper()
        in exception.__str__().upper()
    ):
        # NOTE: Not correct. exception string contains columns names too. It can have reference_number anywhere
        raise BookingReferenceIdCollision(description=exception.__str__())
    if (
        "DUPLICATE KEY VALUE VIOLATES UNIQUE CONSTRAINT".upper()
        in exception.__str__().upper()
    ):
        raise UniqueIdCollision(description=exception.__str__())
    raise PrimaryKeyCollision(description=exception.__str__())


def handle_database_exception(exception):
    session = db_engine.get_scoped_session()
    try:
        # Check if transaction is in aborted state
        if hasattr(session, 'get_transaction') and session.get_transaction():
            if session.get_transaction().is_active:
                session.rollback()
        else:
            session.rollback()
    except Exception as rollback_exception:
        logger.error("Error during rollback: %s", rollback_exception)
        # If rollback fails, try to close and remove the session to force a new one
        try:
            session.close()
            db_engine.get_scoped_session().remove()
        except Exception as cleanup_exception:
            logger.error("Error during session cleanup: %s", cleanup_exception)

    if isinstance(exception, sqlalchemy.exc.IntegrityError) or isinstance(
        exception, FlushError
    ):
        handle_integrity_error(exception)

    if isinstance(exception, sqlalchemy.exc.OperationalError):
        if "could not obtain lock".upper() in exception.__str__().upper():
            raise DatabaseLockError(description=exception.__str__())

    raise DatabaseError(exception.__str__())


class BaseRepository(object):
    """
    Base repository
    """

    @staticmethod
    def session():
        """
        returns session
        :return:
        """
        session = db_engine.get_scoped_session()
        return session

    @staticmethod
    def _ensure_session_is_usable():
        """
        Ensures the current session is usable. If the transaction is aborted,
        creates a new session.
        :return: usable session
        """
        session = db_engine.get_scoped_session()
        try:
            # Test if the session is usable by executing a simple query
            session.execute("SELECT 1")
            return session
        except Exception as e:
            # If session is not usable (e.g., transaction aborted), create a new one
            logger.warning("Session not usable, creating new session. Error: %s", e)
            try:
                session.rollback()
            except Exception:
                pass
            try:
                session.close()
                db_engine.get_scoped_session().remove()
            except Exception as cleanup_exception:
                logger.error("Error during session cleanup: %s", cleanup_exception)
            # Return a fresh session
            return db_engine.get_scoped_session()

    def flush_session(self):
        try:
            session = self._ensure_session_is_usable()
            session.flush()
        except Exception as exp:
            handle_database_exception(exp)

    def create(self, item):
        """
        create new entry in table
        :param item:
        :return:
        """
        session = self._ensure_session_is_usable()
        session.add(item)
        return item

    def create_all(self, items):
        """
        creates multiple items
        :param items:
        :return:
        """
        session = self._ensure_session_is_usable()
        session.add_all(items)
        return items

    def _bulk_save_or_update(self, items):
        try:
            session = self._ensure_session_is_usable()
            session.bulk_save_objects(items)
        except Exception as exp:
            handle_database_exception(exp)

    def update(self, item):
        """
        update an item
        :param item:
        :return: item
        """
        try:
            session = self._ensure_session_is_usable()
            session.add(item)
            return item
        except Exception as exp:
            handle_database_exception(exp)

    def _save(self, item):
        """
        :param item:
        :return:
        """
        try:
            session = self._ensure_session_is_usable()
            session.add(item)
        except Exception as exp:
            handle_database_exception(exp)
        return item

    def _update(self, item):
        """
        :param item:
        :return:
        """
        try:
            session = self._ensure_session_is_usable()
            session.merge(item)
        except Exception as exp:
            handle_database_exception(exp)
        return item

    def _update_all(self, items):
        """
        updates multiple items
        :param items:
        :return:
        """
        session = self._ensure_session_is_usable()
        for item in items:
            session.merge(item)
        return items

    def bulk_update_entities(self, model, entities):
        mappings = annotate_changes(entities, self.from_entity)
        self._bulk_update_mappings(model, mappings)

    def _bulk_update_mappings(self, model, mapping_dicts):
        if not mapping_dicts:
            return
        try:
            session = self._ensure_session_is_usable()
            session.bulk_update_mappings(model, mapping_dicts)
        except Exception as exp:
            handle_database_exception(exp)

    def _bulk_insert_mappings(self, model, mapping_dicts):
        if not mapping_dicts:
            return
        try:
            session = self._ensure_session_is_usable()
            session.bulk_insert_mappings(model, mapping_dicts)
        except Exception as exp:
            handle_database_exception(exp)

    def dumb_save(self, item):
        """
        :param item:
        :return:
        """
        try:
            session = self._ensure_session_is_usable()
            session.add(item)
        except Exception as exp:
            handle_database_exception(exp)

    def _save_all(self, items):
        """
        updates multiple items
        :param items:
        :return:booking
        """
        session = self._ensure_session_is_usable()
        session.add_all(items)
        return items

    def filter(self, model, *queries, for_update=False, nowait=True):
        """
        :param model:
        :param queries:
        :param for_update:
        :param nowait:
        :return:
        """
        session = self._ensure_session_is_usable()
        queryset = session.query(model)
        queryset = queryset.filter(*queries)
        if for_update:
            queryset = queryset.with_for_update(nowait=nowait)
        return queryset

    def exclude(self, query, *exclude_queries):
        real_excludes = [not_(ex) for ex in exclude_queries]
        return query.filter(*real_excludes)

    def mark_deleted(self, model, filter_queries, exclude=None):
        queryset = self.filter(model, *filter_queries)
        if exclude:
            exclude_queries = []
            for item in exclude:
                exclude_queries.append(~item)
            queryset = queryset.filter(*exclude_queries)
        queryset.update({"deleted": True}, synchronize_session=False)

    def filter_by_join(self, models, join_clause, *queries):
        """
        :param models:
        :param join_clause:
        :param queries:
        :return:
        """
        session = self._ensure_session_is_usable()
        queryset = session.query(*models).filter(join_clause)
        items = queryset.filter(*queries)
        return items

    def get_one(self, model, **queries):
        """

        :param model:
        :param queries:
        :return:
        """
        session = self._ensure_session_is_usable()
        queryset = session.query(model)

        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)
        try:
            return queryset.one()
        except NoResultFound:
            return None
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            handle_database_exception(exp)

    def get_for_update(self, model, nowait=True, **queries):
        """
        The query will lock the row that is returned.
        If the transaction cannot lock the row (which will happen when some other transactions have obtained the lock),
        then:
            - If `nowait=True`, the query will fail with error
            - If `nowait=False`, the query will wait for the lock to get released.

        :param model:
        :param nowait:
        :param queries:
        :return:
        """
        session = self._ensure_session_is_usable()
        queryset = session.query(model)
        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)

        # Forcing lazy load here because
        # https://www.postgresql.org/message-id/<EMAIL>
        queryset = queryset.options(lazyload("*"))
        try:
            return queryset.with_for_update(nowait=nowait).one()
        except NoResultFound:
            return None
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            handle_database_exception(exp)

    def delete(self, item):
        """
        delete item
        :param item:
        :return:booking
        """
        session = self._ensure_session_is_usable()
        session.delete(item)

    def rollback_transaction(self):
        """
        rollback transaction
        :return:
        """
        try:
            session = self.session()
            session.rollback()
            session.close()
            db_engine.get_scoped_session().remove()
        except Exception as exp:
            handle_database_exception(exp)

    @abc.abstractmethod
    def to_entity(self, **kwargs):
        pass

    @abc.abstractmethod
    def from_entity(self, entity):
        pass

    def query(self, *model):
        session = self._ensure_session_is_usable()
        return session.query(*model)
