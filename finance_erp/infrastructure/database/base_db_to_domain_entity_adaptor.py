import abc
from datetime import datetime


class BaseAdaptor(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def to_db_entity(self, domain_entity):
        pass

    @abc.abstractmethod
    def to_domain_entity(self, db_entity):
        pass

    @staticmethod
    def to_db_yyyy_mm_dd(_date):
        return (
            datetime.strptime(_date, "%d-%m-%Y").strftime("%Y-%m-%d") if _date else None
        )

    @staticmethod
    def from_db_yyyy_mm_dd(_date):
        return _date.strftime("%d-%m-%Y") if _date else None
