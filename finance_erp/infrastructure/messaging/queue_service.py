import logging
import os
from abc import ABC

from kombu import Connection, Exchange, Producer
from ths_common.exceptions import CRSException
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

logger = logging.getLogger(__name__)


class BaseQueueService(ABC):
    def __init__(self, config):
        self._tenant_wise_producers = dict()
        self.config = config
        self.entity_setup_done = False

    def setup_entities(self):
        for tenant in TenantClient.get_active_tenants():
            tenant_id = tenant.tenant_id
            try:
                rmq_url = AwsSecretManager.get_rmq_url(tenant_id)
            except Exception as e:
                logger.exception(
                    "Can't retrieve RMQ credentials for tenant_id: %s. Skipping",
                    tenant_id,
                )
                continue

            connection = Connection(
                rmq_url, heartbeat=5, transport_options={"confirm_publish": True}
            )
            exchange = Exchange(
                self.config.exchange_name, type=self.config.exchange_type, durable=True
            )
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=connection.channel(), exchange=exchange
            )

    def publish(self, payload, routing_key=None):
        if not self.entity_setup_done:
            self.setup_entities()
            self.entity_setup_done = True
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        producer = self._tenant_wise_producers.get(tenant_id)
        if not producer:
            raise CRSException(
                description=f"RMQ Producer not configured for tenant_id: {tenant_id}"
            )
        logger.info("Publishing event %s", payload)
        producer.publish(
            body=payload,
            routing_key=routing_key,
            retry_policy={
                "interval_start": 0,
                "interval_step": 2,
                "interval_max": 30,
                "max_retries": 3,
            },
            retry=True,
        )
