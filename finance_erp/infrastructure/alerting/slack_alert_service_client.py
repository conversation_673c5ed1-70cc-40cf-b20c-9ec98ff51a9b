import json
import logging
import os

import requests
from flask import current_app as app
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(arguments=[os.environ.get("APP_ENV", "local")])
class SlackAlertServiceClient:
    def __init__(self, environment):
        self.environment = environment

    def record_event(self, event_type, event_payload):
        if self.environment != "production":
            return
        try:
            if hasattr(self, f"get_{event_type}_notifier"):
                url, payload = getattr(self, f"get_{event_type}_notifier")(
                    event_payload
                )
                self._send_slack_alert(payload, slack_url=url)
        except Exception as ex:
            logger.exception(
                "Error while sending slack error on channel. {ex}".format(ex=ex)
            )

    @staticmethod
    def _send_slack_alert(payload, slack_url):
        json_string = json.dumps(payload, default=lambda o: o.__dict__)
        headers = {"content-type": "application/json"}
        response = requests.post(slack_url, data=json_string, headers=headers)
        if response.status_code != 200:
            raise Exception(
                "Received response status: {status}".format(status=response.status_code)
            )

    def get_error_notifier(self, event_payload):
        slack_hook = app.config["FINANCE_ERP_ALERTS"]

        req_id, error, trace, process_name = (
            event_payload.get("req_id"),
            event_payload.get("error"),
            event_payload.get("trace"),
            event_payload.get("process_name"),
        )
        payload = {
            "text": "`(Process: {0}) (Env: {1}) (Req Id: {2}) failed : error {3}`: ```{4}```".format(
                process_name, self.environment, req_id, error, trace
            ),
            "username": "Finance ERP",
        }
        return slack_hook, payload

    def get_data_push_failures_notifier(self, event_payload):
        slack_hook = app.config["FINANCE_ERP_ALERTS"]
        req_id, failures, process_name = (
            event_payload.get("req_id"),
            event_payload.get("failures"),
            event_payload.get("process_name"),
        )
        error_details = ["UUID - Reason"]
        for failure in failures:
            error_details.append(f'{failure.get("uuid")} - {failure.get("reason")}')
        error_details = "\n".join(error_details)
        payload = {
            "text": "`(Process: {0}) (Env: {1}) (Req Id: {2}) has few failures`: ```{3}```".format(
                process_name, self.environment, req_id, error_details
            ),
            "username": "Finance ERP",
        }
        return slack_hook, payload

    def get_job_failure_notifier(self, job_aggregate):
        process_name = (
            f"{job_aggregate.job_entity.job_name} - "
            f'{job_aggregate.job_entity.data.get("report_name") if job_aggregate.job_entity.data else ""}'
        )
        req_id = (
            job_aggregate.job_entity.data.get("event_id", job_aggregate.job_id)
            if job_aggregate.job_entity.data
            else job_aggregate.job_id
        )
        error_payload = dict(
            req_id=req_id,
            error="Job Failed",
            trace=job_aggregate.job_entity.failure_message,
            process_name=process_name,
        )
        return self.get_error_notifier(error_payload)

    @staticmethod
    def send_alert(message):
        slack_url = app.config["FINANCE_ERP_ALERTS"]
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        try:
            app_env = os.environ.get("APP_ENV", "local")
            if app_env not in ["production", "prod"]:
                return

            final_text = message
            payload = {
                "text": "`(Tenant: {0}) Fin ERP ({1})`: ```{2}```".format(
                    tenant_id, app_env.upper(), final_text
                ),
                "username": "Fin ERP",
            }
            json_string = json.dumps(payload, default=lambda o: o.__dict__)
            headers = {"content-type": "application/json"}
            response = requests.post(slack_url, data=json_string, headers=headers)
            if response.status_code != 200:
                raise Exception(
                    "Received response status: {status}".format(
                        status=response.status_code
                    )
                )
        except Exception as ex:
            logger.exception(
                "Error while sending slack error on channel. {ex}".format(ex=ex)
            )
