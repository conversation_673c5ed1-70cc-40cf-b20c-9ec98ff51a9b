import json
import logging

from kombu import Connection, Exchange, Queue, binding
from kombu.mixins import ConsumerMixin


class BaseRMQConsumer(ConsumerMixin):
    def __init__(self, config):
        logger = logging.getLogger(self.__class__.__name__)

        try:
            self.connection = Connection(
                config.rabbitmq_url, transport_options={"confirm_publish": True}
            )
        except Exception as e:
            logger.exception(
                "Error connecting to rabbitmq: %s: %s", config.rabbitmq_url
            )

        self.queues = []
        self.exchange = None
        self._setup_entities(config)

    def _setup_entities(self, config):
        self._setup_exchanges(config)
        self._setup_queue(config)

    def _setup_exchanges(self, config):
        self.exchange = Exchange(config.exchange_name, type=config.exchange_type)

    def _setup_queue(self, config):
        for _queue in config.queues:
            bindings = [
                binding(self.exchange, routing_key=routing_key)
                for routing_key in _queue.routing_keys
            ]
            self.queues.append(
                Queue(_queue.queue_name, bindings, exclusive=config.exclusive)
            )

    def process_message(self, body, message):
        pass

    def get_consumers(self, Consumer, channel):
        consumer = Consumer(queues=self.queues, callbacks=[self.process_message])
        consumer.qos(prefetch_count=1)
        return [consumer]

    def start_consumer(self):
        try:
            self.run()
        except Exception as e:
            logging.exception(e)

    @staticmethod
    def parse_body(body):
        if isinstance(body, bytes):
            body = body.decode()
        if isinstance(body, str):
            body = json.loads(body)
        return body
