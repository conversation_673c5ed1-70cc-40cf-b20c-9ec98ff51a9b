from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class AthenaServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=3000)

    page_map = {
        "fetch_created_or_modified_corporates": dict(
            type=BaseExternalClient.CallTypes.GET, url_regex="/b2b/legal-entities/"
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_athena_service_url()

    def fetch_created_or_modified_corporates(self, request_data):
        legal_entity_ids = request_data.get("legal_entity_ids")
        modified_date = request_data.get("date")
        page_name = "fetch_created_or_modified_corporates"
        data = dict(
            modified_date=modified_date,
            legal_entity_ids=legal_entity_ids,
            fields="legal_entity_id,legal_name,address,trading_name,"
            "phone_number,credit_limit,pan_number,gstin_number,email,tan_number,primary_admin",
        )
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Athena API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response
