from treebo_commons.utils.config_value_parser import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ger<PERSON><PERSON><PERSON>,
    <PERSON>son<PERSON><PERSON><PERSON>,
    <PERSON>Parser,
    StringParser,
)

from finance_erp.infrastructure.external_clients.enums.config_value_type import (
    ConfigValueType,
)


class TenantConfigDto(object):
    PARSER = {
        ConfigValueType.INTEGER: IntegerParser(),
        ConfigValueType.STRING: StringParser(),
        ConfigValueType.JSON: JsonParser(),
        ConfigValueType.BOOLEAN: BooleanParser(),
        ConfigValueType.ARRAY: ListParser(),
        ConfigValueType.DATE: DateParser(),
    }

    def __init__(self, config_name, config_value, value_type):
        self.config_name = config_name
        self.config_value = config_value
        self.value_type = ConfigValueType(value_type)

    def get_config_value(self):
        return self.PARSER.get(self.value_type).parse(self.config_value)
