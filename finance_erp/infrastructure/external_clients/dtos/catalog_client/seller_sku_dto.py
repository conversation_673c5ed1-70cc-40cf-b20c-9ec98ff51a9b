class SellerSkuDTO(object):
    def __init__(self, expense_item_id, name, sku_category):
        self.expense_item_id = expense_item_id
        self.name = name
        self.sku_category = sku_category

    @staticmethod
    def create_from_catalog_seller_sku_data(seller_sku_data):
        return SellerSkuDTO(
            expense_item_id=seller_sku_data.get("sku_id"),
            name=seller_sku_data.get("name"),
            sku_category=seller_sku_data.get("sku_category_code"),
        )
