class BusinessCentralAuthDto:
    def __init__(self, auth_end_point, client_id, client_secret):
        self.auth_end_point = auth_end_point
        self.client_id = client_id
        self.client_secret = client_secret
        self.grant_type = "client_credentials"
        self.scope = "https://api.businesscentral.dynamics.com/.default"
        self.access_token = None

    def set_access_token(self, token):
        self.access_token = token


class BusinessCentralConfig:
    def __init__(
        self,
        auth_end_point,
        end_point_details,
        client_id,
        client_secret,
        api_endpoint,
    ):
        self.api_endpoint = api_endpoint
        self.end_point_details = end_point_details
        self.auth_config: BusinessCentralAuthDto = BusinessCentralAuthDto(
            auth_end_point, client_id, client_secret
        )

    def get_access_token(self):
        return self.auth_config.access_token
