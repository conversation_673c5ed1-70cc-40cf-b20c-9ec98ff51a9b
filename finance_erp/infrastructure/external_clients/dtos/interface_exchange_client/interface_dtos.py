class PaymentMappingDTO:
    def __init__(self, external_id, internal_id):
        self.external_id = external_id
        self.internal_id = internal_id

    @staticmethod
    def create_from_payment_data(payment_data):
        return PaymentMappingDTO(
            external_id=payment_data.get("external_id"),
            internal_id=payment_data.get("internal_id"),
        )


class SkuMappingDTO:
    def __init__(self, external_id, internal_id, hsn_code, sku_category):
        self.external_id = external_id
        self.internal_id = internal_id
        self.hsn_code = hsn_code
        self.sku_category = sku_category

    @staticmethod
    def create_from_sku_data(sku_data):
        return SkuMappingDTO(
            external_id=sku_data.get("external_id"),
            internal_id=sku_data.get("internal_id"),
            hsn_code=sku_data.get("hsn_code"),
            sku_category=sku_data.get("sku_category"),
        )


class RevenueCenterMappingDTO:
    def __init__(
        self,
        revenue_center_mapping_id,
        external_id,
        internal_id,
        payment_mappings,
        sku_mappings,
    ):
        self.revenue_center_mapping_id = revenue_center_mapping_id
        self.external_id = external_id
        self.internal_id = internal_id
        self.payment_mappings = payment_mappings
        self.sku_mappings = sku_mappings

    @staticmethod
    def create_from_revenue_center_data(revenue_center_data):
        payment_mappings = [
            PaymentMappingDTO.create_from_payment_data(pm)
            for pm in revenue_center_data.get("payment_mappings", [])
        ]
        sku_mappings = [
            SkuMappingDTO.create_from_sku_data(sm)
            for sm in revenue_center_data.get("sku_mappings", [])
        ]

        return RevenueCenterMappingDTO(
            revenue_center_mapping_id=revenue_center_data.get(
                "revenue_center_mapping_id"
            ),
            external_id=revenue_center_data.get("external_id"),
            internal_id=revenue_center_data.get("internal_id"),
            payment_mappings=payment_mappings,
            sku_mappings=sku_mappings,
        )


class InterfaceDTO(object):
    def __init__(
        self,
        interface_id,
        interface_type,
        interface_name,
        hardware_provider,
        revenue_center_mappings,
    ):
        self.interface_id = interface_id
        self.interface_type = interface_type
        self.interface_name = interface_name
        self.hardware_provider = hardware_provider
        self.revenue_center_mappings = revenue_center_mappings

    @staticmethod
    def create_from_interfaces_data(interface_data):
        revenue_center_mappings = [
            RevenueCenterMappingDTO.create_from_revenue_center_data(rcm)
            for rcm in interface_data.get("revenue_center_mappings", [])
        ]
        return InterfaceDTO(
            interface_id=interface_data.get("interface_id"),
            interface_type=interface_data.get("interface_type"),
            interface_name=interface_data.get("name"),
            hardware_provider=interface_data.get("hardware_provider"),
            revenue_center_mappings=revenue_center_mappings,
        )
