from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class ARServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=4000)

    page_map = {
        "pull_payment_data": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/ar/v1/reports/pull-payment-report",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_account_receivable_service_url()

    def pull_payment_data(self, resource_data, report_name):
        page_name = "pull_payment_data"
        data = dict(data=dict(report_name=report_name, resource_data=resource_data))
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "AR API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.data
