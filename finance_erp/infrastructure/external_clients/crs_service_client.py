from ths_common.constants.user_constants import UserType

from finance_erp.common.exception import CRSServiceException
from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class CrsServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=4000)

    page_map = {
        "finance_report": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/v1/finance-reports",
        ),
        "fetch_finance_reports": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/v1/fetch-finance-reports",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_crs_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        headers["X-User-Type"] = UserType.SUPER_ADMIN.value
        return headers

    def trigger_async_report_generation(self, date, report_name):
        page_name = "finance_report"
        data = dict(date=date, report_names=report_name)
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Reseller API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def fetch_finance_reports(self, resource_data, report_name):
        page_name = "fetch_finance_reports"
        data = dict(data=dict(report_name=report_name, resource_data=resource_data))
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise CRSServiceException(
                "CRS API (fetch_finance_reports: {0}) Error. Status Code: {1}, Errors: {2}".format(
                    report_name, response.response_code, response.errors
                )
            )
        return response.data
