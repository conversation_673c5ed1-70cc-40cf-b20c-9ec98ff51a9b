import enum
import logging
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

logger = logging.getLogger(__name__)


class ServiceEndPointNames(enum.Enum):
    NOTIFICATION_SERVICE_URL = "notification_service_url"
    RESELLER_SERVICE_ENDPOINT_URL = "reseller_service_url"
    CRS_SERVICE_URL = "crs_service_url"
    CATALOG_SERVICE_URL = "catalog_service_url"
    ATHENA_SERVICE_URL = "athena_service_url"
    COMPANY_PROFILE_SERVICE_URL = "company_profile_service_url"
    TEMPLATE_SERVICE_URL = "template_service_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"
    ROLE_MANAGER_SERVICE_URL = "role_manager_service_url"
    TAX_SERVICE_URL = "tax_service_url"
    INTERFACE_EXCHANGE_SERVICE_URL = "interface_exchange_service_url"
    ACCOUNT_RECEIVABLE_SERVICE_URL = "account_receivable_service_url"


class ServiceRegistryClient:

    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_notification_service_url(cls):
        service_name = ServiceEndPointNames.NOTIFICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_reseller_service_url(cls):
        service_name = ServiceEndPointNames.RESELLER_SERVICE_ENDPOINT_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_crs_service_url(cls):
        service_name = ServiceEndPointNames.CRS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_athena_service_url(cls):
        service_name = ServiceEndPointNames.ATHENA_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_company_profile_service_url(cls):
        service_name = ServiceEndPointNames.COMPANY_PROFILE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_template_service_url(cls):
        service_name = ServiceEndPointNames.TEMPLATE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_role_manager_service_url(cls):
        service_name = ServiceEndPointNames.ROLE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tax_service_url(cls):
        service_name = ServiceEndPointNames.TAX_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_interface_exchange_service_url(cls):
        service_name = ServiceEndPointNames.INTERFACE_EXCHANGE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_account_receivable_service_url(cls):
        service_name = ServiceEndPointNames.ACCOUNT_RECEIVABLE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
