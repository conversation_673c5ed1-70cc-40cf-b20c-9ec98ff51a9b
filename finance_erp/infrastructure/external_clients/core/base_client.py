import logging
import time
from abc import abstractmethod

import requests
from requests.models import PreparedRequest
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from finance_erp.infrastructure.external_clients.core.base_response_object import (
    BaseResponseObject,
)

logger = logging.getLogger(__name__)


class BaseExternalClient(object):
    """
    BaseExternalClient
    """

    page_map = dict()

    def __init__(self, timeout=30, log_handler=None):
        self.logger = logger or log_handler
        self.timeout = timeout

    class CallTypes(object):
        """
        CallTypes
        """

        POST = "post"
        GET = "get"
        PUT = "put"
        DELETE = "delete"

        call_mapper = {
            GET: requests.get,
            POST: requests.post,
            PUT: requests.put,
            DELETE: requests.delete,
        }

        @classmethod
        def get_request(cls, call_type):
            """
            :param call_type:
            :return:
            """
            return cls.call_mapper.get(call_type)

    def get_page_map(self):
        """
        returns the pagename -> url map for the external api's
        Returns:

        """
        return self.page_map

    @staticmethod
    def get_headers():
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)
        return headers

    @abstractmethod
    def get_domain(self):
        """
        The domain of the external service being called. Need to be implemented by each client.
        :return: Domain of the external system
        """
        pass

    def get_api(self, page_name, **kwargs):
        ret_val = dict(self.get_page_map()[page_name])
        ret_val["url"] = ret_val["url_regex"].format(**kwargs)
        return ret_val

    def make_call(
        self, page_name, data=None, url_parameters=None, optional_url_params=None
    ):
        """
        API to make the external call
        :param data:
        :param page_name:
        :param url_parameters:
        :param optional_url_params:
        :return:
        """
        if not url_parameters:
            url_parameters = dict()
        api = self.get_api(page_name, **url_parameters)
        api_type = api.get("type")
        url = self.get_domain() + api.get("url")
        if optional_url_params:
            req = PreparedRequest()
            req.prepare_url(url, optional_url_params)
            url = req.url

        headers = self.get_headers()

        try:
            request_params = dict(
                url=url, headers=headers, allow_redirects=True, timeout=self.timeout
            )

            if api_type == self.CallTypes.GET:
                request_params["params"] = data
            else:
                request_params["json"] = data

            start = time.time()
            response = self.CallTypes.get_request(api_type)(**request_params)
            response_object = BaseResponseObject(response)
            time_taken = round(time.time() - start, 3)

            if response_object.status_code not in {200, 201}:
                logger.error(
                    "Failure Response %s:: For %s API call to url: %s with headers: %s. Time taken: %s secs, "
                    "Request Payload: %s, Response: %s",
                    self.__class__.__name__,
                    api_type.upper(),
                    url,
                    headers,
                    time_taken,
                    data,
                    response_object,
                )

            else:
                logger.info(
                    "Success Response %s:: For %s API call to url: %s with headers: %s. Time taken: %s secs, "
                    "Request Payload: %s, Response: %s",
                    self.__class__.__name__,
                    api_type.upper(),
                    url,
                    headers,
                    time_taken,
                    data,
                    response_object,
                )

        except Exception as e:
            logger.exception("BaseExternalClient Exception")
            return BaseResponseObject(None)

        return response_object
