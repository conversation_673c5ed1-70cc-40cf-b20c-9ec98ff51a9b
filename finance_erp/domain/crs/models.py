from typing import Dict

from sqlalchemy import (
    ARRAY,
    DECIMAL,
    JSON,
    Boolean,
    Column,
    Date,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
)
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import TimeStampMixin


class InvoiceModel(Base, TimeStampMixin):
    __tablename__ = "invoice"

    invoice_id = Column(String, primary_key=True)
    invoice_number = Column(String, nullable=False)
    status = Column(String, nullable=False)
    booking_reference_number = Column(String, nullable=False)
    booking_id = Column(String, nullable=False)

    customer_external_id = Column(String)
    booker_legal_entity_id = Column(String)
    issued_to = Column(JSON)
    issued_by = Column(JSON)
    booking_meta = Column(JSON)
    issued_to_type = Column(String)
    issued_by_type = Column(String)
    vendor_details = Column(JSON)

    pre_tax_amount = Column(DECIMAL, nullable=False)
    tax_amount = Column(DECIMAL, nullable=False)
    post_tax_amount = Column(DECIMAL, nullable=False)

    due_date = Column(Date)
    invoice_date = Column(Date, nullable=False)

    is_b2b = Column(Boolean, nullable=False)
    invoice_type = Column(String)
    is_spot_credit = Column(Boolean)

    invoice_url = Column(String)
    hotel_id = Column(String, nullable=False)
    frozen_at = Column(DateTime(timezone=True))

    __table_args__ = (
        Index("ix_invoice_invoice_number", "invoice_number"),
        Index("ix_invoice_invoice_date", "invoice_date"),
        Index("ix_invoice_booking_booking_id", "booking_id"),
        Index("ix_invoice_invoice_id", "invoice_id"),
        Index("ix_invoice_hotel_id", "hotel_id"),
        Index(
            "ix_invoice_is_b2b_invoice_type_booker",
            "booker_legal_entity_id",
            "is_b2b",
            "invoice_type",
            "invoice_date",
            "status",
        ),
    )

    def mapping_dict(self) -> Dict:
        return {
            "invoice_id": self.invoice_id,
            "invoice_number": self.invoice_number,
            "status": self.status,
            "booking_reference_number": self.booking_reference_number,
            "booking_id": self.booking_id,
            "customer_external_id": self.customer_external_id,
            "booker_legal_entity_id": self.booker_legal_entity_id,
            "issued_to": self.issued_to,
            "issued_by": self.issued_by,
            "booking_meta": self.booking_meta,
            "vendor_details": self.vendor_details,
            "issued_to_type": self.issued_to_type,
            "issued_by_type": self.issued_by_type,
            "pre_tax_amount": self.pre_tax_amount,
            "tax_amount": self.tax_amount,
            "post_tax_amount": self.post_tax_amount,
            "due_date": self.due_date,
            "invoice_date": self.invoice_date,
            "is_b2b": self.is_b2b,
            "invoice_type": self.invoice_type,
            "is_spot_credit": self.is_spot_credit,
            "invoice_url": self.invoice_url,
            "hotel_id": self.hotel_id,
        }


class CreditNoteModel(Base, TimeStampMixin):
    __tablename__ = "credit_note"

    credit_note_id = Column(String, primary_key=True)
    credit_note_number = Column(String, nullable=False)
    booking_reference_number = Column(String, nullable=False)
    booking_id = Column(String, nullable=False)

    customer_external_id = Column(String)
    booker_legal_entity_id = Column(String)
    issued_to = Column(JSON)
    issued_by = Column(JSON)
    booking_meta = Column(JSON)
    invoice_meta = Column(JSON)
    vendor_details = Column(JSON)
    issued_to_type = Column(String)
    issued_by_type = Column(String)

    pre_tax_amount = Column(DECIMAL, nullable=False)
    tax_amount = Column(DECIMAL, nullable=False)
    post_tax_amount = Column(DECIMAL, nullable=False)

    credit_note_date = Column(Date, nullable=False)

    is_b2b = Column(Boolean, nullable=False)
    credit_note_type = Column(String)
    is_spot_credit = Column(Boolean)
    invoice_ids = Column(ARRAY(String), nullable=False)

    credit_note_url = Column(String)
    hotel_id = Column(String, nullable=False)

    __table_args__ = (
        Index("ix_credit_note_credit_note_number", "credit_note_number"),
        Index("ix_credit_note_credit_note_date", "credit_note_date"),
        Index("ix_credit_note_booking_id", "booking_id"),
        Index("ix_credit_note_credit_note_id", "credit_note_id"),
        Index("ix_credit_note_hotel_id", "hotel_id"),
        Index(
            "ix_credit_note_is_b2b_invoice_type_booker",
            "is_b2b",
            "credit_note_type",
            "booker_legal_entity_id",
        ),
    )

    def mapping_dict(self) -> Dict:
        return {
            "credit_note_id": self.credit_note_id,
            "credit_note_number": self.credit_note_number,
            "booking_reference_number": self.booking_reference_number,
            "booking_id": self.booking_id,
            "customer_external_id": self.customer_external_id,
            "booker_legal_entity_id": self.booker_legal_entity_id,
            "issued_to": self.issued_to,
            "issued_by": self.issued_by,
            "booking_meta": self.booking_meta,
            "vendor_details": self.vendor_details,
            "invoice_meta": self.invoice_meta,
            "issued_to_type": self.issued_to_type,
            "issued_by_type": self.issued_by_type,
            "pre_tax_amount": self.pre_tax_amount,
            "tax_amount": self.tax_amount,
            "post_tax_amount": self.post_tax_amount,
            "credit_note_date": self.credit_note_date,
            "is_b2b": self.is_b2b,
            "credit_note_type": self.credit_note_type,
            "is_spot_credit": self.is_spot_credit,
            "invoice_ids": self.invoice_ids,
            "credit_note_url": self.credit_note_url,
            "hotel_id": self.hotel_id,
        }


class BookingModel(Base, TimeStampMixin):
    __tablename__ = "booking"

    booking_id = Column(String, primary_key=True)
    reference_number = Column(String, nullable=False)
    checkin_date = Column(DateTime, nullable=False)
    checkout_date = Column(DateTime, nullable=False)
    actual_checkin_date = Column(DateTime)
    actual_checkout_date = Column(DateTime)
    channel_code = Column(String, nullable=False)
    subchannel_code = Column(String, nullable=False)
    application_code = Column(String, nullable=False)
    status = Column(String, nullable=False)
    booker_legal_entity_id = Column(String)
    total_booking_amount = Column(DECIMAL, nullable=False)
    attachments = Column(JSON)

    __table_args__ = (
        Index("ix_booking_booking_id", "booking_id"),
        Index("ix_booking_reference_number", "reference_number"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "booking_id": self.booking_id,
            "checkin_date": self.checkin_date,
            "checkout_date": self.checkout_date,
            "actual_checkin_date": self.actual_checkin_date,
            "actual_checkout_date": self.actual_checkout_date,
            "channel_code": self.channel_code,
            "subchannel_code": self.subchannel_code,
            "application_code": self.application_code,
            "booker_legal_entity_id": self.booker_legal_entity_id,
            "total_booking_amount": self.total_booking_amount,
            "attachments": self.attachments,
        }


class StaySummaryModel(Base, TimeStampMixin):
    __tablename__ = "stay_summary"

    stay_summary_id = Column(String, primary_key=True)
    corporate_id = Column(String, nullable=False)
    billing_from = Column(Date, nullable=False)
    billing_to = Column(Date, nullable=False)
    billing_date = Column(Date, nullable=False)
    signed_url = Column(String)
    booking_request_signed_url = Column(String)

    invoices = relationship(
        "StaySummaryInvoiceModel", back_populates="stay_summary", lazy="select"
    )
    credit_notes = relationship(
        "StaySummaryCreditNoteModel", back_populates="stay_summary", lazy="select"
    )

    __table_args__ = (Index("ix_stay_summary_corporate_id", "corporate_id"),)

    def mapping_dict(self) -> Dict:
        return {
            "stay_summary_id": self.stay_summary_id,
            "corporate_id": self.corporate_id,
            "billing_from": self.billing_from,
            "billing_to": self.billing_to,
            "signed_url": self.signed_url,
            "booking_request_signed_url": self.booking_request_signed_url,
        }


class StaySummaryInvoiceModel(Base):
    __tablename__ = "stay_summary_invoice"

    invoice_id = Column(String, ForeignKey("invoice.invoice_id"), primary_key=True)
    stay_summary_id = Column(
        String, ForeignKey("stay_summary.stay_summary_id"), primary_key=True
    )
    stay_summary = relationship("StaySummaryModel", back_populates="invoices")

    def mapping_dict(self) -> Dict:
        return {
            "invoice_id": self.invoice_id,
            "stay_summary_id": self.stay_summary_id,
        }


class StaySummaryCreditNoteModel(Base):
    __tablename__ = "stay_summary_credit_note"

    credit_note_id = Column(
        String, ForeignKey("credit_note.credit_note_id"), primary_key=True
    )
    stay_summary_id = Column(
        String, ForeignKey("stay_summary.stay_summary_id"), primary_key=True
    )
    stay_summary = relationship("StaySummaryModel", back_populates="credit_notes")

    def mapping_dict(self) -> Dict:
        return {
            "credit_note_id": self.credit_note_id,
            "stay_summary_id": self.stay_summary_id,
        }


class StaySummarySequenceModel(Base, TimeStampMixin):
    __tablename__ = "stay_summary_sequence"

    corporate_code = Column(String, primary_key=True)
    sequence = Column(Integer, nullable=False)

    def mapping_dict(self) -> Dict:
        return {
            "corporate_id": self.corporate_id,
            "sequence": self.sequence,
        }
