from typing import Dict, List, Optional

from pydantic import BaseModel, Field, PrivateAttr

from finance_erp.common.constants import InvoiceTypes
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.entity.stay_summary import StaySummary
from finance_erp.domain.crs.value_objects.booking import Attachment


class StaySummaryAggregate(BaseModel):
    stay_summary: StaySummary
    invoices: Optional[List[Invoice]] = Field(default_factory=list)
    credit_notes: Optional[List[CreditNote]] = Field(default_factory=list)
    _booking_request_attachments: Dict[str, Attachment] = PrivateAttr(
        default_factory=dict
    )
    _due_amount: Optional[float] = PrivateAttr(default=None)
    _paid_invoice_amount: Optional[float] = PrivateAttr(default=None)

    def __init__(self, **data: dict) -> None:
        super().__init__(**data)
        self._booking_request_attachments = {
            inv.booking_meta.booking_request_attachment.attachment_id: inv.booking_meta.booking_request_attachment
            for inv in self.invoices
            if inv.booking_meta and inv.booking_meta.booking_request_attachment
        }
        self._booking_request_attachments.update(
            {
                cn.booking_meta.booking_request_attachment.attachment_id: cn.booking_meta.booking_request_attachment
                for cn in self.credit_notes
                if cn.booking_meta and cn.booking_meta.booking_request_attachment
            }
        )
        invoice_dues = sum(inv.due_amount for inv in self.invoices)
        sum_of_credit_cns = sum(
            cn.post_tax_amount
            for cn in self.credit_notes
            if cn.post_tax_amount is not None
            and InvoiceTypes.is_credit(cn.credit_note_type)
        )
        reissued_invoices = {
            cn.invoice_meta.get("invoice_number") for cn in self.credit_notes
        }
        self._paid_invoice_amount = sum(
            inv.paid_amount
            for inv in self.invoices
            if inv.invoice_number not in reissued_invoices
        )
        self._due_amount = invoice_dues - sum_of_credit_cns

    def update_signed_url(self, signed_url: str):
        self.stay_summary.signed_url = signed_url

    def update_booking_request_signed_url(self, booking_request_signed_url: str):
        self.stay_summary.booking_request_signed_url = booking_request_signed_url

    def get_all_booking_request_attachments(self):
        return self._booking_request_attachments.values()

    def has_documents(self):
        return bool(self.invoices or self.credit_notes)

    @property
    def stay_summary_id(self):
        return self.stay_summary.stay_summary_id

    @property
    def due_amount(self):
        return self._due_amount

    @property
    def paid_invoice_amount(self):
        return self._paid_invoice_amount

    @property
    def from_date(self):
        return self.stay_summary.billing_from

    @property
    def to_date(self):
        return self.stay_summary.billing_to

    @property
    def signed_url(self):
        return self.stay_summary.signed_url

    @property
    def booking_request_archive_url(self):
        return self.stay_summary.booking_request_signed_url

    @property
    def inv_total_amount(self):
        inv_amount = (
            sum(inv.post_tax_amount for inv in self.invoices) if self.invoices else 0
        )
        return inv_amount

    @property
    def cn_total_amount(self):
        inv_amount = (
            sum(cn.post_tax_amount for cn in self.credit_notes)
            if self.credit_notes
            else 0
        )
        return inv_amount

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        validate_by_name = True
