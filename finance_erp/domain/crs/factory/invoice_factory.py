from datetime import timedelta

from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from thsc.crs.entities.billing import Invoice as THSCInvoice

from finance_erp.common.constants import InvoiceTypes
from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.value_objects.booking import BookingMeta


class InvoiceFactory:
    @classmethod
    def build(
        cls, thsc_invoice: THSCInvoice, booking: Booking, credit_period
    ) -> Invoice:
        due_date = None
        if thsc_invoice.invoice_date and credit_period:
            due_date = thsc_invoice.invoice_date + timedelta(days=credit_period)
        data_dict = {
            "invoice_id": thsc_invoice.invoice_id,
            "invoice_number": thsc_invoice.invoice_number,
            "status": thsc_invoice.status,
            "customer_external_id": thsc_invoice.bill_to.external_ref_id,
            "booker_legal_entity_id": (
                booking.booker_legal_entity_id or thsc_invoice.bill_to.external_ref_id
            ),
            "issued_to": thsc_invoice.bill_to,
            "issued_by": thsc_invoice.issued_by,
            "issued_to_type": thsc_invoice.issued_to_type,
            "issued_by_type": thsc_invoice.issued_by_type,
            "invoice_type": cls._get_invoice_type(thsc_invoice),
            "is_b2b": thsc_invoice.bill_to_type == ChargeBillToTypes.COMPANY,
            "is_spot_credit": thsc_invoice.is_spot_credit,
            "invoice_url": thsc_invoice.signed_url,
            "due_date": due_date,
            "pre_tax_amount": thsc_invoice.pretax_amount.amount,
            "tax_amount": thsc_invoice.tax_amount.amount,
            "post_tax_amount": thsc_invoice.posttax_amount.amount,
            "booking_id": thsc_invoice.parent_info.get("booking_id"),
            "booking_reference_number": thsc_invoice.parent_info.get(
                "reference_number"
            ),
            "hotel_id": thsc_invoice.vendor_details.get("vendor_id"),
            "invoice_date": thsc_invoice.invoice_date,
            "booking_meta": BookingMeta.from_booking_entity(booking),
            "vendor_details": thsc_invoice.vendor_details,
        }
        return Invoice.model_validate(data_dict)

    @classmethod
    def _get_invoice_type(cls, thsc_invoice: THSCInvoice) -> str:
        is_credit = any(
            charge_type == ChargeTypes.CREDIT
            for charge_type in thsc_invoice.allowed_charge_types
        )
        if is_credit:
            return (
                InvoiceTypes.SPOT_CREDIT
                if thsc_invoice.is_spot_credit
                else InvoiceTypes.NON_SPOT_CREDIT
            )
        return InvoiceTypes.NON_CREDIT
