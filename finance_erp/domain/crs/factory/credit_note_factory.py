from thsc.crs.entities.billing import CreditNote as THSCCreditNote

from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice


class CreditNoteFactory:
    @classmethod
    def build(cls, thsc_credit_note: THSCCreditNote, invoice: Invoice) -> CreditNote:
        data_dict = {
            "credit_note_id": thsc_credit_note.credit_note_id,
            "credit_note_number": thsc_credit_note.credit_note_number,
            "credit_note_date": thsc_credit_note.credit_note_date,
            "booking_id": invoice.booking_id,
            "booking_reference_number": invoice.booking_reference_number,
            "hotel_id": invoice.hotel_id,
            "customer_external_id": invoice.customer_external_id,
            "booker_legal_entity_id": invoice.booker_legal_entity_id,
            "issued_to": thsc_credit_note.issued_to,
            "issued_by": thsc_credit_note.issued_by,
            "issued_to_type": thsc_credit_note.issued_to_type,
            "issued_by_type": thsc_credit_note.issued_by_type,
            "invoice_ids": list(
                {
                    line_item.invoice_id
                    for line_item in thsc_credit_note.credit_note_line_items
                }
            ),
            "invoice_meta": {
                "invoice_number": invoice.invoice_number,
                "invoice_date": invoice.invoice_date.isoformat()
                if invoice.invoice_date
                else None,
            },
            "credit_note_type": invoice.invoice_type,
            "is_b2b": invoice.is_b2b,
            "is_spot_credit": invoice.is_spot_credit,
            "credit_note_url": thsc_credit_note.signed_url,
            "pre_tax_amount": thsc_credit_note.pretax_amount.amount,
            "tax_amount": thsc_credit_note.tax_amount.amount,
            "post_tax_amount": thsc_credit_note.posttax_amount.amount,
            "booking_meta": invoice.booking_meta,
            "vendor_details": invoice.vendor_details,
        }
        return CreditNote.model_validate(data_dict)
