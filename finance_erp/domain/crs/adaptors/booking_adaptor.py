from ths_common.constants.booking_constants import BookingStatus

from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.models import BookingModel
from finance_erp.domain.crs.value_objects.booking import Attachment


class BookingAdaptor:
    @staticmethod
    def to_entity(model: BookingModel) -> Booking:
        return Booking(
            booking_id=model.booking_id,
            reference_number=model.reference_number,
            checkin_date=model.checkin_date,
            checkout_date=model.checkout_date,
            actual_checkin_date=model.actual_checkin_date,
            actual_checkout_date=model.actual_checkout_date,
            channel_code=model.channel_code,
            subchannel_code=model.subchannel_code,
            application_code=model.application_code,
            status=BookingStatus(model.status) if model.status else None,
            booker_legal_entity_id=model.booker_legal_entity_id,
            total_booking_amount=float(model.total_booking_amount),
            attachments=[
                Attachment.from_dict(attachment) for attachment in model.attachments
            ]
            if model.attachments
            else None,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def from_entity(entity: Booking) -> BookingModel:
        # noinspection PyArgumentList
        return BookingModel(
            booking_id=entity.booking_id,
            reference_number=entity.reference_number,
            checkin_date=entity.checkin_date,
            checkout_date=entity.checkout_date,
            actual_checkin_date=entity.actual_checkin_date,
            actual_checkout_date=entity.actual_checkout_date,
            channel_code=entity.channel_code,
            subchannel_code=entity.subchannel_code,
            application_code=entity.application_code,
            status=entity.status.value if entity.status else None,
            booker_legal_entity_id=entity.booker_legal_entity_id,
            total_booking_amount=entity.total_booking_amount,
            attachments=[attachment.to_dict() for attachment in entity.attachments]
            if entity.attachments
            else None,
            created_at=entity.created_at,
            modified_at=entity.modified_at,
        )
