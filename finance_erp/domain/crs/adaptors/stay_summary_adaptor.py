from finance_erp.domain.crs.entity.stay_summary import StaySummary
from finance_erp.domain.crs.models import StaySummaryModel


class StaySummaryAdapter:
    @staticmethod
    def to_entity(stay_summary_model: StaySummaryModel) -> StaySummary:
        return StaySummary(
            corporate_id=stay_summary_model.corporate_id,
            billing_from=stay_summary_model.billing_from,
            billing_to=stay_summary_model.billing_to,
            stay_summary_id=stay_summary_model.stay_summary_id,
            billing_date=stay_summary_model.billing_date,
            signed_url=stay_summary_model.signed_url,
            booking_request_signed_url=stay_summary_model.booking_request_signed_url,
            created_at=stay_summary_model.created_at,
            modified_at=stay_summary_model.modified_at,
        )

    @staticmethod
    def from_entity(stay_summary_domain: StaySummary) -> StaySummaryModel:
        # noinspection PyArgumentList
        return StaySummaryModel(
            corporate_id=stay_summary_domain.corporate_id,
            billing_from=stay_summary_domain.billing_from,
            billing_to=stay_summary_domain.billing_to,
            stay_summary_id=stay_summary_domain.stay_summary_id,
            billing_date=stay_summary_domain.billing_date,
            signed_url=stay_summary_domain.signed_url,
            booking_request_signed_url=stay_summary_domain.booking_request_signed_url,
            created_at=stay_summary_domain.created_at,
            modified_at=stay_summary_domain.modified_at,
        )
