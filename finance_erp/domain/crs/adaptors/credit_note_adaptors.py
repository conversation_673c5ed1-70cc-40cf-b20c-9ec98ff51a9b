from ths_common.value_objects import InvoiceBillToInfo, InvoiceIssuedByInfo

from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.models import CreditNoteModel
from finance_erp.domain.crs.value_objects.booking import BookingMeta


class CreditNoteAdaptor:
    @staticmethod
    def to_entity(model: CreditNoteModel) -> CreditNote:
        return CreditNote(
            credit_note_id=model.credit_note_id,
            credit_note_number=model.credit_note_number,
            booking_reference_number=model.booking_reference_number,
            booking_id=model.booking_id,
            customer_external_id=model.customer_external_id,
            booker_legal_entity_id=model.booker_legal_entity_id,
            issued_to=InvoiceBillToInfo.from_json(model.issued_to)
            if model.issued_to
            else None,
            issued_by=InvoiceIssuedByInfo.from_json(model.issued_by)
            if model.issued_by
            else None,
            booking_meta=BookingMeta.from_dict(model.booking_meta)
            if model.booking_meta
            else None,
            invoice_meta=model.invoice_meta,
            issued_to_type=model.issued_to_type,
            issued_by_type=model.issued_by_type,
            pre_tax_amount=model.pre_tax_amount,
            tax_amount=model.tax_amount,
            post_tax_amount=model.post_tax_amount,
            credit_note_date=model.credit_note_date,
            is_b2b=model.is_b2b,
            credit_note_type=model.credit_note_type,
            is_spot_credit=model.is_spot_credit,
            invoice_ids=model.invoice_ids,
            credit_note_url=model.credit_note_url,
            hotel_id=model.hotel_id,
            vendor_details=model.vendor_details,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def from_entity(entity: CreditNote) -> CreditNoteModel:
        # noinspection PyArgumentList
        return CreditNoteModel(
            credit_note_id=entity.credit_note_id,
            credit_note_number=entity.credit_note_number,
            booking_reference_number=entity.booking_reference_number,
            booking_id=entity.booking_id,
            customer_external_id=entity.customer_external_id,
            booker_legal_entity_id=entity.booker_legal_entity_id,
            issued_to=entity.issued_to.to_json() if entity.issued_to else None,
            issued_by=entity.issued_by.to_json() if entity.issued_by else None,
            booking_meta=entity.booking_meta.to_dict() if entity.booking_meta else None,
            invoice_meta=entity.invoice_meta,
            issued_to_type=entity.issued_to_type,
            issued_by_type=entity.issued_by_type,
            pre_tax_amount=entity.pre_tax_amount,
            tax_amount=entity.tax_amount,
            post_tax_amount=entity.post_tax_amount,
            credit_note_date=entity.credit_note_date,
            is_b2b=entity.is_b2b,
            credit_note_type=entity.credit_note_type,
            is_spot_credit=entity.is_spot_credit,
            invoice_ids=entity.invoice_ids,
            credit_note_url=entity.credit_note_url,
            hotel_id=entity.hotel_id,
            vendor_details=entity.vendor_details,
            created_at=entity.created_at,
            modified_at=entity.modified_at,
        )
