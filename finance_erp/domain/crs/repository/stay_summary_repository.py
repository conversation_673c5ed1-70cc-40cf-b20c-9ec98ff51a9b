from datetime import date
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy import and_
from ths_common.constants.billing_constants import InvoiceStatus

from finance_erp.common.constants import FIN_ERP_MINT_MIGRATION_GUARD_DATE
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.domain.crs.adaptors.credit_note_adaptors import CreditNoteAdaptor
from finance_erp.domain.crs.adaptors.invoice_adaptors import InvoiceAdaptor
from finance_erp.domain.crs.adaptors.stay_summary_adaptor import StaySummaryAdapter
from finance_erp.domain.crs.aggregate.stay_summary_aggregate import StaySummaryAggregate
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.entity.stay_summary import StaySummary
from finance_erp.domain.crs.models import (
    BookingModel,
    CreditNoteModel,
    InvoiceModel,
    StaySummaryCreditNoteModel,
    StaySummaryInvoiceModel,
    StaySummaryModel,
    StaySummarySequenceModel,
)
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


class StaySummaryIdGenerationStrategy(object):
    @staticmethod
    def generate_id(corporate_code, sequence_number):
        return f"SS/{corporate_code.upper()}/{sequence_number}"


@register_instance()
class StaySummaryRepository(BaseRepository):
    def from_entity(
        self, aggregate: StaySummaryAggregate
    ) -> Tuple[
        StaySummaryModel,
        List[StaySummaryInvoiceModel],
        List[StaySummaryCreditNoteModel],
    ]:
        stay_summary_model = StaySummaryAdapter.from_entity(aggregate.stay_summary)
        # noinspection PyArgumentList
        stay_summary_invoice_models = [
            StaySummaryInvoiceModel(
                stay_summary_id=stay_summary_model.stay_summary_id,
                invoice_id=invoice.invoice_id,
                stay_summary=stay_summary_model,
            )
            for invoice in aggregate.invoices
        ]
        stay_summary_cn_models = [
            StaySummaryCreditNoteModel(
                stay_summary_id=stay_summary_model.stay_summary_id,
                credit_note_id=credit_notes.credit_note_id,
                stay_summary=stay_summary_model,
            )
            for credit_notes in aggregate.credit_notes
        ]
        return stay_summary_model, stay_summary_invoice_models, stay_summary_cn_models

    def to_entity(self, entity: StaySummaryModel) -> StaySummary:
        return StaySummaryAdapter.to_entity(entity)

    def to_aggregate(
        self,
        stay_summary_model: StaySummaryModel,
        invoice_models: Optional[List[InvoiceModel]],
        credit_note_models: Optional[List[BookingModel]],
    ) -> StaySummaryAggregate:
        stay_summary = self.to_entity(stay_summary_model)
        invoices = (
            [InvoiceAdaptor.to_entity(invoice) for invoice in invoice_models]
            if invoice_models
            else []
        )
        credit_notes = (
            [CreditNoteAdaptor.to_entity(model) for model in credit_note_models]
            if credit_note_models
            else []
        )
        return StaySummaryAggregate(
            stay_summary=stay_summary,
            invoices=invoices,
            credit_notes=credit_notes,
        )

    def save(self, aggregate: StaySummaryAggregate) -> StaySummaryAggregate:
        (
            stay_summary_model,
            stay_summary_invoice_models,
            stay_summary_cn_models,
        ) = self.from_entity(aggregate)
        self._save(stay_summary_model)
        return aggregate

    def get_by_id(
        self,
        stay_summary_id: str,
        load_invoices=False,
        load_cns=False,
    ) -> Optional[StaySummaryAggregate]:
        summary = self.get_one(StaySummaryModel, stay_summary_id=stay_summary_id)

        if not summary:
            return None

        invoices = (
            self._fetch_associated_invoices(stay_summary_id) if load_invoices else None
        )
        credit_notes = (
            self._fetch_associated_credit_notes(stay_summary_id) if load_cns else None
        )

        return self.to_aggregate(summary, invoices, credit_notes)

    def _fetch_associated_invoices(self, stay_summary_id):
        invoices = (
            self.query(InvoiceModel)
            .join(
                StaySummaryInvoiceModel,
                InvoiceModel.invoice_id == StaySummaryInvoiceModel.invoice_id,
            )
            .filter(StaySummaryInvoiceModel.stay_summary_id == stay_summary_id)
        )
        return invoices

    def _fetch_associated_credit_notes(self, stay_summary_id):
        invoices = (
            self.query(CreditNoteModel)
            .join(
                StaySummaryCreditNoteModel,
                CreditNoteModel.credit_note_id
                == StaySummaryCreditNoteModel.credit_note_id,
            )
            .filter(StaySummaryCreditNoteModel.stay_summary_id == stay_summary_id)
        )
        return invoices

    def get_eligible_corporate_invoices_for_stay_summary(
        self,
        corporate_id: str,
        bill_to: date,
        invoice_types,
    ) -> List[Invoice]:
        query = (
            self.query(InvoiceModel)
            .outerjoin(
                StaySummaryInvoiceModel,
                InvoiceModel.invoice_id == StaySummaryInvoiceModel.invoice_id,
            )
            .filter(
                and_(
                    InvoiceModel.booker_legal_entity_id == corporate_id,
                    InvoiceModel.is_b2b.is_(True),
                    InvoiceModel.invoice_type.in_(invoice_types),
                    InvoiceModel.invoice_date <= bill_to,
                    InvoiceModel.invoice_date >= FIN_ERP_MINT_MIGRATION_GUARD_DATE,
                    InvoiceModel.status != InvoiceStatus.CANCELLED.value,
                    StaySummaryInvoiceModel.stay_summary_id.is_(None),
                )
            )
        )
        return [InvoiceAdaptor.to_entity(invoice) for invoice in query.all()]

    def get_eligible_credit_notes_for_stay_summary(
        self,
        corporate_id: str,
        bill_to: date,
        credit_note_types,
    ) -> List[CreditNote]:
        query = (
            self.query(CreditNoteModel)
            .outerjoin(
                StaySummaryCreditNoteModel,
                CreditNoteModel.credit_note_id
                == StaySummaryCreditNoteModel.credit_note_id,
            )
            .filter(
                and_(
                    CreditNoteModel.booker_legal_entity_id == corporate_id,
                    CreditNoteModel.is_b2b.is_(True),
                    CreditNoteModel.credit_note_type.in_(credit_note_types),
                    CreditNoteModel.credit_note_date <= bill_to,
                    CreditNoteModel.credit_note_date
                    >= FIN_ERP_MINT_MIGRATION_GUARD_DATE,
                    StaySummaryCreditNoteModel.stay_summary_id.is_(None),
                )
            )
        )
        return [CreditNoteAdaptor.to_entity(invoice) for invoice in query.all()]

    def get_next_stay_summary_id(self, corporate_code):
        sequence = self.get_for_update(
            StaySummarySequenceModel, corporate_code=corporate_code
        )

        if not sequence:
            _ = self.get_for_update(
                CorporateModel, corporate_code=corporate_code, nowait=False
            )
            sequence = self.get_for_update(
                StaySummarySequenceModel, corporate_code=corporate_code, nowait=False
            )
            if not sequence:
                latest_sequence = (
                    self.session()
                    .query(StaySummarySequenceModel)
                    .filter(StaySummarySequenceModel.corporate_code == corporate_code)
                    .order_by(StaySummarySequenceModel.modified_at.desc())
                    .first()
                )

                sequence_number = (
                    1 if not latest_sequence else latest_sequence.sequence + 1
                )
                # noinspection PyArgumentList
                new_sequence = StaySummarySequenceModel(
                    corporate_code=corporate_code, sequence=sequence_number
                )
                current_sequence = self._save(new_sequence)
            else:
                sequence_number = sequence.sequence + 1
                sequence.sequence = sequence_number
                current_sequence = self._save(sequence)
        else:
            sequence_number = sequence.sequence + 1
            sequence.sequence = sequence_number
            current_sequence = self._save(sequence)

        stay_summary_id = StaySummaryIdGenerationStrategy.generate_id(
            corporate_code, sequence_number
        )

        self.flush_session()
        return stay_summary_id
