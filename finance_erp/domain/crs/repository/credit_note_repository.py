from typing import List

from sqlalchemy import String, cast, func

from finance_erp.domain.crs.adaptors.credit_note_adaptors import CreditNoteAdaptor
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.models import CreditNoteModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class CreditNoteRepository(BaseRepository):
    def to_entity(self, model: CreditNoteModel) -> CreditNote:
        return CreditNoteAdaptor.to_entity(model)

    def from_entity(self, entity: CreditNote) -> CreditNoteModel:
        return CreditNoteAdaptor.from_entity(entity)

    def save(self, entity: CreditNote) -> CreditNote:
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def get_by_id(self, credit_note_id: str) -> CreditNote:
        model = super().get_one(CreditNoteModel, credit_note_id=credit_note_id)
        return self.to_entity(model) if model else None

    def update(self, entity: CreditNote) -> CreditNote:
        if not entity.dirty:
            return entity
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def get_by_booking_ids(self, booking_ids: List[str]) -> List[CreditNote]:
        query = self.query(CreditNoteModel).filter(
            CreditNoteModel.booking_id.in_(booking_ids)
        )
        return [self.to_entity(model) for model in query.all()]

    def get_credit_note_counts_by_date(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        invoice_numbers,
        booking_reference_numbers,
        hotel_id=None,
        credit_note_type=None,
    ):
        query = self.query(CreditNoteModel).filter()
        if start_date:
            query = query.filter(CreditNoteModel.credit_note_date >= start_date)
        if end_date:
            query = query.filter(CreditNoteModel.credit_note_date <= end_date)
        if booker_legal_entity_ids:
            query = query.filter(
                CreditNoteModel.booker_legal_entity_id.in_(booker_legal_entity_ids)
            )
        if booking_reference_numbers:
            query = query.filter(
                CreditNoteModel.booking_reference_number.in_(booking_reference_numbers)
            )
        if invoice_numbers:
            query = query.filter(
                cast(CreditNoteModel.invoice_meta["invoice_number"], String).in_(
                    invoice_numbers
                )
            )
        if hotel_id:
            query = query.filter(CreditNoteModel.hotel_id == hotel_id)
        if credit_note_type:
            query = query.filter(CreditNoteModel.credit_note_type.in_(credit_note_type))

        grouped_query = query.with_entities(
            CreditNoteModel.credit_note_date.label("date"),
            func.count().label("count"),
        ).group_by(CreditNoteModel.credit_note_date)

        return grouped_query.order_by(CreditNoteModel.credit_note_date.desc()).all()

    def get_credit_notes_for_booker_legal_entity_id(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        invoice_numbers,
        booking_reference_numbers,
        hotel_id=None,
        credit_note_type=None,
    ):
        query = self.query(CreditNoteModel).filter(
            CreditNoteModel.credit_note_date >= start_date,
            CreditNoteModel.credit_note_date <= end_date,
        )
        if booker_legal_entity_ids:
            query = query.filter(
                CreditNoteModel.booker_legal_entity_id.in_(booker_legal_entity_ids)
            )
        if booking_reference_numbers:
            query = query.filter(
                CreditNoteModel.booking_reference_number.in_(booking_reference_numbers)
            )
        if invoice_numbers:
            query = query.filter(
                cast(CreditNoteModel.invoice_meta["invoice_number"], String).in_(
                    invoice_numbers
                )
            )
        if hotel_id:
            query = query.filter(CreditNoteModel.hotel_id == hotel_id)
        if credit_note_type:
            query = query.filter(CreditNoteModel.credit_note_type.in_(credit_note_type))

        return [self.to_entity(model) for model in query.all()]
