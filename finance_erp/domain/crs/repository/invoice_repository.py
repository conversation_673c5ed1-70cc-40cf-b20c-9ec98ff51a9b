from typing import List

from sqlalchemy import func

from finance_erp.domain.crs.adaptors.invoice_adaptors import InvoiceAdaptor
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.models import InvoiceModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class InvoiceRepository(BaseRepository):
    def to_entity(self, model: InvoiceModel) -> Invoice:
        return InvoiceAdaptor.to_entity(model)

    def from_entity(self, entity: "Invoice") -> InvoiceModel:
        return InvoiceAdaptor.from_entity(entity)

    def save(self, entity: "Invoice") -> "Invoice":
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def get_by_invoice_id(self, invoice_id: str) -> Invoice:
        model = super().get_one(InvoiceModel, invoice_id=invoice_id)
        return self.to_entity(model) if model else None

    def update(self, entity: Invoice) -> Invoice:
        if not entity.dirty:
            return entity
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def get_by_invoice_ids(self, invoice_ids: List[str]) -> List[Invoice]:
        query = self.query(InvoiceModel).filter(
            InvoiceModel.invoice_id.in_(invoice_ids)
        )
        return [self.to_entity(model) for model in query.all()]

    def get_by_booking_ids(self, booking_ids: List[str]) -> List[Invoice]:
        query = self.query(InvoiceModel).filter(
            InvoiceModel.booking_id.in_(booking_ids)
        )
        return [self.to_entity(model) for model in query.all()]

    def get_invoice_counts_by_date(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        invoice_numbers,
        booking_reference_numbers,
        hotel_id=None,
        invoice_type=None,
    ):
        query = self.query(InvoiceModel).filter()
        if start_date:
            query = query.filter(
                InvoiceModel.invoice_date >= start_date,
            )
        if end_date:
            query = query.filter(InvoiceModel.invoice_date <= end_date)
        if booker_legal_entity_ids:
            query = query.filter(
                InvoiceModel.booker_legal_entity_id.in_(booker_legal_entity_ids)
            )
        if invoice_numbers:
            query = query.filter(InvoiceModel.invoice_number.in_(invoice_numbers))
        if booking_reference_numbers:
            query = query.filter(
                InvoiceModel.booking_reference_number.in_(booking_reference_numbers)
            )
        if hotel_id:
            query = query.filter(InvoiceModel.hotel_id == hotel_id)
        if invoice_type:
            query = query.filter(InvoiceModel.invoice_type.in_(invoice_type))

        grouped_query = query.with_entities(
            InvoiceModel.invoice_date.label("date"),
            func.count().label("count"),
        ).group_by(InvoiceModel.invoice_date)

        return grouped_query.order_by(InvoiceModel.invoice_date.desc()).all()

    def get_invoices_for_booker_legal_entity_id(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        invoice_numbers,
        booking_reference_numbers,
        hotel_id=None,
        invoice_type=None,
    ):
        query = self.query(InvoiceModel).filter(
            InvoiceModel.invoice_date >= start_date,
            InvoiceModel.invoice_date <= end_date,
        )
        if booker_legal_entity_ids:
            query = query.filter(
                InvoiceModel.booker_legal_entity_id.in_(booker_legal_entity_ids)
            )
        if invoice_numbers:
            query = query.filter(InvoiceModel.invoice_number.in_(invoice_numbers))
        if booking_reference_numbers:
            query = query.filter(
                InvoiceModel.booking_reference_number.in_(booking_reference_numbers)
            )
        if hotel_id:
            query = query.filter(InvoiceModel.hotel_id == hotel_id)
        if invoice_type:
            query = query.filter(InvoiceModel.invoice_type.in_(invoice_type))

        return [self.to_entity(model) for model in query.all()]
