from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel


class StaySummary(BaseModel):
    stay_summary_id: str
    corporate_id: str
    billing_from: date
    billing_to: date
    billing_date: date
    signed_url: Optional[str] = None
    booking_request_signed_url: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        validate_by_name = True
