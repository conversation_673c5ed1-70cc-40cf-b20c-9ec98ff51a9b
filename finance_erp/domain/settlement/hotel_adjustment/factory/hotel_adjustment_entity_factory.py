from finance_erp.domain.settlement.hotel_adjustment.entity.hotel_adjustment import (
    HotelAdjustmentEntity,
)


class HotelAdjustmentFactory:
    @staticmethod
    def create_settlement_hotel_adjustment_from_data_dict(data_dict: dict):
        data_dict.update(
            {
                "uu_id": f'{data_dict["entry_type"]}/{data_dict["adjustment_type"]}/{data_dict["hotel_code"]}/{data_dict.get("posting_date")}',
            }
        )
        return HotelAdjustmentEntity.model_validate(data_dict)
