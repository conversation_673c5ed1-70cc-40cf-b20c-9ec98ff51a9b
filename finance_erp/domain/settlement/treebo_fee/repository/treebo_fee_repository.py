from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.settlement.treebo_fee.adaptors.treebo_fee_adaptor import (
    TreeboFeeAdaptor,
)
from finance_erp.domain.settlement.treebo_fee.entity.treebo_fee import TreeboFeeEntity
from finance_erp.domain.settlement.treebo_fee.models import TreeboFeeModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class TreeboFeeRepository(BaseRepository):
    settlement_treebo_fee_adaptor = TreeboFeeAdaptor()

    def to_entity(self, model):
        return self.settlement_treebo_fee_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.settlement_treebo_fee_adaptor.to_db_entity(entity)

    def insert_many(self, settlement_treebo_fee_list: List[TreeboFeeEntity]):
        self._bulk_insert_mappings(
            TreeboFeeModel,
            [
                self.from_entity(settlement_treebo_fee).mapping_dict()
                for settlement_treebo_fee in settlement_treebo_fee_list
            ],
        )
        self.flush_session()

    def bulk_update_records(self, settlement_treebo_fee_list: List[TreeboFeeEntity]):
        self._bulk_update_mappings(
            TreeboFeeModel,
            [
                self.from_entity(settlement_treebo_fee).mapping_dict()
                for settlement_treebo_fee in settlement_treebo_fee_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(TreeboFeeModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            TreeboFeeModel,
            TreeboFeeModel.uu_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(TreeboFeeModel)
            .filter(
                TreeboFeeModel.modified_at >= from_date_gte,
                TreeboFeeModel.modified_at <= to_date_lte,
                TreeboFeeModel.deleted == False,
            )
            .order_by(TreeboFeeModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: TreeboFeeEntity):
        self.bulk_update_entities(TreeboFeeModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(TreeboFeeModel)
            .filter(
                TreeboFeeModel.verified == True,
                TreeboFeeModel.status.in_(
                    ERPEntityStatus.allowed_status_for_data_push()
                ),
                TreeboFeeModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(TreeboFeeModel)
            .filter(
                TreeboFeeModel.uu_id.in_(tuple(uu_ids)),
                TreeboFeeModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(TreeboFeeModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
