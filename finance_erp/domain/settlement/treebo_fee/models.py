from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class TreeboFeeModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "settlement_treebo_fee"

    hotel_code = Column(String)
    settlement_date = Column(Date)
    uu_id = Column(String, primary_key=True)
    remarks = Column(String)
    description = Column(String)
    amount = Column(DECIMAL)
    gst_percent = Column(DECIMAL)
    entry_type = Column(String)
    hsn_code = Column(String)
    doc_type = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "hotel_code": self.hotel_code,
            "settlement_date": self.settlement_date,
            "remarks": self.remarks,
            "description": self.description,
            "uu_id": self.uu_id,
            "amount": self.amount,
            "gst_percent": self.gst_percent,
            "entry_type": self.entry_type,
            "hsn_code": self.hsn_code,
            "doc_type": self.doc_type,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
