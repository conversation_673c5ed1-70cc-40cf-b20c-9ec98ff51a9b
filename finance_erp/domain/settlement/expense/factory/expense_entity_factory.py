from finance_erp.domain.settlement.expense.entity.expense import ExpenseEntity


class ExpenseFactory:
    @staticmethod
    def create_settlement_expense_from_data_dict(data_dict: dict):
        data_dict.update(
            {
                "uu_id": f'{data_dict["entry_type"]}/{data_dict["hotel_code"]}/{data_dict.get("posting_date")}',
            }
        )
        return ExpenseEntity.model_validate(data_dict)
