from finance_erp.domain.settlement.expense.entity.expense import ExpenseEntity
from finance_erp.domain.settlement.expense.models import ExpenseModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class ExpenseAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: ExpenseEntity, **kwargs):
        # noinspection PyArgumentList
        return ExpenseModel(
            hotel_code=domain_entity.hotel_code,
            posting_date=domain_entity.posting_date,
            uu_id=domain_entity.uu_id,
            tds_per=domain_entity.tds_per,
            invoice_number=domain_entity.invoice_number,
            invoice_amount=domain_entity.invoice_amount,
            invoice_date=domain_entity.invoice_date
            if domain_entity.invoice_date
            else None,
            entry_type=domain_entity.entry_type,
            hsn_code=domain_entity.hsn_code,
            doc_type=domain_entity.doc_type,
            remarks=domain_entity.remarks,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: ExpenseModel, **kwargs):
        return ExpenseEntity(
            hotel_code=db_entity.hotel_code,
            posting_date=db_entity.posting_date,
            tds_per=db_entity.tds_per,
            invoice_number=db_entity.invoice_number,
            invoice_amount=db_entity.invoice_amount,
            invoice_date=db_entity.invoice_date if db_entity.invoice_date else None,
            entry_type=db_entity.entry_type,
            hsn_code=db_entity.hsn_code,
            doc_type=db_entity.doc_type,
            remarks=db_entity.remarks,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
        )
