from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.settlement.expense.adaptors.expense_adaptor import (
    ExpenseAdaptor,
)
from finance_erp.domain.settlement.expense.entity.expense import ExpenseEntity
from finance_erp.domain.settlement.expense.models import ExpenseModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class ExpenseRepository(BaseRepository):
    settlement_expense_adaptor = ExpenseAdaptor()

    def to_entity(self, model):
        return self.settlement_expense_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.settlement_expense_adaptor.to_db_entity(entity)

    def insert_many(self, settlement_expense_list: List[ExpenseEntity]):
        self._bulk_insert_mappings(
            ExpenseModel,
            [
                self.from_entity(settlement_expense).mapping_dict()
                for settlement_expense in settlement_expense_list
            ],
        )
        self.flush_session()

    def bulk_update_records(self, settlement_expense_list: List[ExpenseEntity]):
        self._bulk_update_mappings(
            ExpenseModel,
            [
                self.from_entity(settlement_expense).mapping_dict()
                for settlement_expense in settlement_expense_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(ExpenseModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            ExpenseModel, ExpenseModel.uu_id.in_(tuple(uu_ids)), for_update=for_update
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(ExpenseModel)
            .filter(
                ExpenseModel.modified_at >= from_date_gte,
                ExpenseModel.modified_at <= to_date_lte,
                ExpenseModel.deleted == False,
            )
            .order_by(ExpenseModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: ExpenseEntity):
        self.bulk_update_entities(ExpenseModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(ExpenseModel)
            .filter(
                ExpenseModel.verified == True,
                ExpenseModel.status.in_(ERPEntityStatus.allowed_status_for_data_push()),
                ExpenseModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(ExpenseModel)
            .filter(
                ExpenseModel.uu_id.in_(tuple(uu_ids)),
                ExpenseModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(ExpenseModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
