from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class LoanModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "settlement_loan"

    hotel_code = Column(String)
    posting_date = Column(Date)
    uu_id = Column(String, primary_key=True)
    loan_amount = Column(DECIMAL)
    entry_type = Column(String)
    doc_type = Column(String)
    remarks = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "hotel_code": self.hotel_code,
            "posting_date": self.posting_date,
            "remarks": self.remarks,
            "uu_id": self.uu_id,
            "loan_amount": self.loan_amount,
            "entry_type": self.entry_type,
            "doc_type": self.doc_type,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
