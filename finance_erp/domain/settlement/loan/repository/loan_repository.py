from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.settlement.loan.adaptors.loan_adaptor import LoanAdaptor
from finance_erp.domain.settlement.loan.entity.loan import LoanEntity
from finance_erp.domain.settlement.loan.models import LoanModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class LoanRepository(BaseRepository):
    settlement_loan_adaptor = LoanAdaptor()

    def to_entity(self, model):
        return self.settlement_loan_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.settlement_loan_adaptor.to_db_entity(entity)

    def insert_many(self, settlement_loan_list: List[LoanEntity]):
        self._bulk_insert_mappings(
            LoanModel,
            [
                self.from_entity(settlement_loan).mapping_dict()
                for settlement_loan in settlement_loan_list
            ],
        )
        self.flush_session()

    def bulk_update_records(self, settlement_loan_list: List[LoanEntity]):
        self._bulk_update_mappings(
            LoanModel,
            [
                self.from_entity(settlement_loan).mapping_dict()
                for settlement_loan in settlement_loan_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(LoanModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            LoanModel, LoanModel.uu_id.in_(tuple(uu_ids)), for_update=for_update
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(LoanModel)
            .filter(
                LoanModel.modified_at >= from_date_gte,
                LoanModel.modified_at <= to_date_lte,
                LoanModel.deleted == False,
            )
            .order_by(LoanModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: LoanEntity):
        self.bulk_update_entities(LoanModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(LoanModel)
            .filter(
                LoanModel.verified == True,
                LoanModel.status.in_(ERPEntityStatus.allowed_status_for_data_push()),
                LoanModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(LoanModel)
            .filter(
                LoanModel.uu_id.in_(tuple(uu_ids)),
                LoanModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(LoanModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
