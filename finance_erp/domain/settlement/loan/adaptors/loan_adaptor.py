import datetime

from finance_erp.domain.settlement.loan.entity.loan import LoanEntity
from finance_erp.domain.settlement.loan.models import LoanModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class LoanAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: LoanEntity, **kwargs):
        # noinspection PyArgumentList
        return LoanModel(
            hotel_code=domain_entity.hotel_code,
            posting_date=domain_entity.posting_date,
            uu_id=domain_entity.uu_id,
            loan_amount=domain_entity.loan_amount,
            entry_type=domain_entity.entry_type,
            doc_type=domain_entity.doc_type,
            remarks=domain_entity.remarks,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: LoanModel, **kwargs):
        return LoanEntity(
            hotel_code=db_entity.hotel_code,
            posting_date=db_entity.posting_date,
            loan_amount=db_entity.loan_amount,
            entry_type=db_entity.entry_type,
            doc_type=db_entity.doc_type,
            remarks=db_entity.remarks,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
        )
