from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.settlement.tax.adaptors.tax_adaptor import TaxAdaptor
from finance_erp.domain.settlement.tax.entity.tax import TaxEntity
from finance_erp.domain.settlement.tax.models import TaxModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class TaxRepository(BaseRepository):
    settlement_tax_adaptor = TaxAdaptor()

    def to_entity(self, model):
        return self.settlement_tax_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.settlement_tax_adaptor.to_db_entity(entity)

    def insert_many(self, settlement_tax_list: List[TaxEntity]):
        self._bulk_insert_mappings(
            TaxModel,
            [
                self.from_entity(settlement_tax).mapping_dict()
                for settlement_tax in settlement_tax_list
            ],
        )
        self.flush_session()

    def bulk_update_records(self, settlement_tax_list: List[TaxEntity]):
        self._bulk_update_mappings(
            TaxModel,
            [
                self.from_entity(settlement_tax).mapping_dict()
                for settlement_tax in settlement_tax_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(TaxModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            TaxModel, TaxModel.uu_id.in_(tuple(uu_ids)), for_update=for_update
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(TaxModel)
            .filter(
                TaxModel.modified_at >= from_date_gte,
                TaxModel.modified_at <= to_date_lte,
                TaxModel.deleted == False,
            )
            .order_by(TaxModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: TaxEntity):
        self.bulk_update_entities(TaxModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(TaxModel)
            .filter(
                TaxModel.verified == True,
                TaxModel.status.in_(ERPEntityStatus.allowed_status_for_data_push()),
                TaxModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(TaxModel)
            .filter(
                TaxModel.uu_id.in_(tuple(uu_ids)),
                TaxModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(TaxModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
