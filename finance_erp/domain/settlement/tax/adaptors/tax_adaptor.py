import datetime

from finance_erp.domain.settlement.tax.entity.tax import TaxEntity
from finance_erp.domain.settlement.tax.models import TaxModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class TaxAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: TaxEntity, **kwargs):
        # noinspection PyArgumentList
        return TaxModel(
            hotel_code=domain_entity.hotel_code,
            posting_date=domain_entity.posting_date,
            uu_id=domain_entity.uu_id,
            cgst_amount=domain_entity.cgst_amount,
            sgst_amount=domain_entity.sgst_amount,
            total_amount=domain_entity.total_amount,
            entry_type=domain_entity.entry_type,
            doc_type=domain_entity.doc_type,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: TaxModel, **kwargs):
        return TaxEntity(
            hotel_code=db_entity.hotel_code,
            posting_date=db_entity.posting_date,
            cgst_amount=db_entity.cgst_amount,
            sgst_amount=db_entity.sgst_amount,
            total_amount=db_entity.total_amount,
            entry_type=db_entity.entry_type,
            doc_type=db_entity.doc_type,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
        )
