from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, field_validator

from finance_erp.application.common.utils import (
    get_country_code,
    get_state_code,
    sanitize_string,
)
from finance_erp.common.constants import GSTType
from finance_erp.domain.company_profile.dto.company_profiles_dto import SubEntity
from finance_erp.domain.company_profile.value_object import (
    CommunicationSettings,
    CorporatePOC,
)


class CorporateDto(BaseModel):
    customer_legal_name: str
    customer_trading_name: str
    address: str
    city: Optional[str] = None
    phone_number: Optional[str] = None
    post_code: Optional[str] = None
    email: Optional[str] = None
    credit_limit: Optional[str] = None
    credit_period: Optional[int] = None
    country_code: Optional[str] = None
    pan: Optional[str] = None
    state_code: Optional[str] = None
    gstin: Optional[str] = None
    gst_customer_type: str
    corporate_code: str
    tan_number: Optional[str] = None
    billing_period: Optional[int] = None
    communication_settings: "CommunicationSettings"
    corporate_pocs: List["CorporatePOC"]
    external_account_number: Optional[str] = None

    @field_validator("credit_limit", mode="before")
    def parse_credit_limit(cls, v):
        if isinstance(v, float):
            return str(v)
        if isinstance(v, int):
            return str(v)
        return v

    @staticmethod
    def from_dict(data: Dict) -> "CorporateDto":
        communication_settings = CommunicationSettings(
            **data.get("communication_settings", {})
        )
        corporate_pocs = [CorporatePOC(**poc) for poc in data.get("corporate_pocs", [])]
        data_dict = {
            "customer_legal_name": data.get("customer_legal_name"),
            "customer_trading_name": data.get("customer_trading_name"),
            "address": data.get("address"),
            "city": data.get("city"),
            "phone_number": data.get("phone_number"),
            "post_code": data.get("post_code"),
            "email": data.get("email"),
            "credit_limit": data.get("credit_limit"),
            "credit_period": data.get("credit_period"),
            "country_code": data.get("country_code"),
            "pan": data.get("pan"),
            "state_code": data.get("state_code"),
            "gstin": data.get("gstin"),
            "gst_customer_type": data.get("gst_customer_type"),
            "corporate_code": data.get("corporate_code"),
            "tan_number": data.get("tan_number"),
            "billing_period": data.get("billing_period"),
            "external_account_number": data.get("external_account_number"),
            "communication_settings": communication_settings,
            "corporate_pocs": corporate_pocs,
        }
        return CorporateDto.model_validate(data_dict)

    @staticmethod
    def create_from_company_profile_entity(entity: SubEntity):
        phone = (
            entity.phone_number
            if entity.phone_number
            else entity.primary_admin.phone_number
            if entity.primary_admin
            else None
        )
        email = (
            entity.email_id
            if entity.email_id
            else entity.primary_admin.email_ids[0]
            if entity.primary_admin and entity.primary_admin.email_ids
            else None
        )
        address = entity.address
        data_dict = {
            "customer_legal_name": entity.legal_entity_name,
            "customer_trading_name": entity.trade_name,
            "address": entity.address_string,
            "city": address.city if address else None,
            "phone_number": str(phone) if phone else None,
            "post_code": entity.address.pincode if entity.address else None,
            "email": email,
            "credit_limit": entity.credit_settings.credit_limit
            if entity.credit_settings
            else None,
            "credit_period": entity.credit_settings.credit_period
            if entity.credit_settings
            else None,
            "country_code": get_country_code(address.country)
            if address and address.country
            else None,
            "pan": entity.pan_number,
            "state_code": get_state_code(address.state)
            if address and address.state
            else None,
            "gstin": entity.gstin,
            "gst_customer_type": (
                GSTType.REGISTERED.value if entity.gstin else GSTType.UNREGISTERED.value
            ),
            "corporate_code": entity.superhero_company_code,
            "tan_number": entity.tan_number,
            "billing_period": entity.billing_period,
            "external_account_number": entity.external_account_number,
            "communication_settings": entity.communication_settings,
            "corporate_pocs": entity.point_of_contacts,
        }
        return CorporateDto.model_validate(data_dict)

    # TODO: Remove this part of [PROM-17203]
    @staticmethod
    def from_corporate_data_response(corporate_data: Dict[str, Any]) -> "CorporateDto":
        primary_admin = corporate_data.get("primary_admin")
        communication_settings = corporate_data.get("communication_settings")
        point_of_contacts = corporate_data.get("point_of_contacts")
        address = corporate_data["address"]
        customer_trading_name = corporate_data["trading_name"]
        post_code = corporate_data["address"]["pincode"]
        credit_limit = corporate_data["credit_limit"]
        credit_period = corporate_data["credit_period"]
        country_code = get_country_code(corporate_data["address"]["country"])
        state_code = get_state_code(corporate_data["address"]["state"])
        pan = corporate_data["pan_number"]
        gstin = corporate_data["gstin_number"]
        corporate_code = corporate_data["legal_entity_id"]
        tan_number = corporate_data.get("tan_number", "")
        customer_legal_name = sanitize_string(corporate_data["legal_name"], length=80)
        address_str = (
            sanitize_string(
                f"{address['building']}{address['street']}{address['locality']}{address['landmark']}",
                length=100,
            )
            if address
            else None
        )
        city = sanitize_string(
            corporate_data["address"]["city"], length=30, only_alpha=True
        )
        phone_number = (
            corporate_data["phone_number"] or primary_admin.get("phone_number")
            if primary_admin
            else None
        )
        email = (
            sanitize_string(corporate_data["email"], 30)
            if corporate_data["email"]
            else primary_admin.get("email")
            if primary_admin
            else None
        )
        gst_customer_type = (
            GSTType.REGISTERED.value if gstin else GSTType.UNREGISTERED.value
        )
        billing_period = corporate_data.get("billing_period")
        external_account_number = corporate_data.get("external_account_number")
        communication_settings_instance = (
            CommunicationSettings(**communication_settings)
            if communication_settings
            else None
        )
        corporate_pocs_instances = (
            [CorporatePOC(**poc) for poc in point_of_contacts]
            if point_of_contacts
            else []
        )
        data_dict = {
            "customer_legal_name": customer_legal_name,
            "customer_trading_name": customer_trading_name,
            "address": address_str,
            "city": city,
            "phone_number": phone_number,
            "post_code": post_code,
            "email": email,
            "credit_limit": credit_limit,
            "credit_period": credit_period,
            "country_code": country_code,
            "pan": pan,
            "state_code": state_code,
            "gstin": gstin,
            "gst_customer_type": gst_customer_type,
            "corporate_code": corporate_code,
            "tan_number": tan_number,
            "billing_period": billing_period,
            "external_account_number": external_account_number,
            "communication_settings": communication_settings_instance,
            "corporate_pocs": corporate_pocs_instances,
        }
        return CorporateDto.model_validate(data_dict)

    def to_dict(self):
        data = self.model_dump()
        data["communication_settings"] = (
            self.communication_settings.to_dict()
            if self.communication_settings
            else None
        )
        data["corporate_pocs"] = (
            [poc.to_dict() for poc in self.corporate_pocs]
            if self.corporate_pocs
            else None
        )
        return data

    def get_unique_identifier(self):
        return self.corporate_code

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
