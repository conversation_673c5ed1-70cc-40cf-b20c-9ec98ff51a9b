from ths_common.utils.common_utils import safe_strip
from ths_common.value_objects import PhoneNumber

from core.common.utils import sanitize_string
from finance_erp.domain.company_profile.constants import ProfilePOCRoles
from finance_erp.domain.company_profile.value_object import (
    CommunicationSettings,
    CorporatePOC,
)


class Address:
    def __init__(self, address_line_1, address_line_2, city, country, pincode, state):
        self.address_line_1 = address_line_1
        self.address_line_2 = address_line_2
        self.city = city
        self.country = country
        self.pincode = pincode
        self.state = state

    def __str__(self):
        address_str = f"{self.address_line_1}"
        if self.address_line_2:
            address_str += f", {self.address_line_2}"
        address_str += f", {self.city}, {self.state}, {self.country}, {self.pincode}"
        return address_str


class StatutoryDetail:
    def __init__(self, attachment_url, field_name, value):
        self.attachment_url = attachment_url
        self.field_name = field_name
        self.value = value


class CreditSettings:
    def __init__(self, credit_amount, credit_period, **kwargs):
        self.credit_amount = credit_amount
        self.credit_period = credit_period


class RegisteredAddress(Address):
    pass


class CommunicationAddress(Address):
    pass


class SubEntity:
    def __init__(
        self,
        client_internal_code,
        communication_address,
        created_at,
        email_id,
        has_lut,
        is_sez_applicable,
        legal_entity_name,
        parent_entity_id,
        parent_superhero_company_code,
        point_of_contacts,
        registered_address,
        status,
        statutory_details,
        sub_entity_id,
        superhero_company_code,
        trade_name,
        external_account_number=None,
        phone_number=None,
        communication_settings=None,
        billing_period=None,
        credit_settings=None,
        is_test=False,
        **kwargs,
    ):
        self.client_internal_code = client_internal_code
        self.communication_address = (
            CommunicationAddress(**communication_address)
            if communication_address
            else None
        )
        self.created_at = created_at
        self.email_id = email_id
        self.has_lut = has_lut
        self.is_sez_applicable = is_sez_applicable
        self.legal_entity_name = legal_entity_name
        self.parent_entity_id = parent_entity_id
        self.parent_superhero_company_code = parent_superhero_company_code
        self.point_of_contacts = (
            [CorporatePOC(**poc) for poc in point_of_contacts]
            if point_of_contacts
            else []
        )
        self.registered_address = (
            RegisteredAddress(**registered_address) if registered_address else None
        )
        self.status = status
        self.statutory_details = (
            [StatutoryDetail(**detail) for detail in statutory_details]
            if statutory_details
            else []
        )
        self.sub_entity_id = sub_entity_id
        self.superhero_company_code = superhero_company_code
        self.trade_name = trade_name
        self.external_account_number = external_account_number
        self.billing_period = (
            int(billing_period) if billing_period is not None else None
        )
        self.poc_mappings = {poc.designation: poc for poc in self.point_of_contacts}
        self._statutory_details = {
            detail.field_name: detail for detail in self.statutory_details
        }
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None
        self.communication_settings = (
            CommunicationSettings(**communication_settings)
            if communication_settings
            else None
        )
        self.credit_settings = (
            CreditSettings(**credit_settings) if credit_settings else None
        )
        self.is_test = is_test

    @property
    def gstin(self):
        gst = self._statutory_details.get("gst")
        return safe_strip(gst.value) if gst and gst.value else None

    @property
    def pan_number(self):
        pan = self._statutory_details.get("pan_number")
        return safe_strip(pan.value) if pan and pan.value else None

    @property
    def tan_number(self):
        tan = self._statutory_details.get("tan_number")
        return safe_strip(tan.value) if tan and tan.value else None

    @property
    def primary_admin(self):
        return self.poc_mappings.get(ProfilePOCRoles.PRIMARY_ADMIN)

    @property
    def address(self):
        return self.communication_address or self.registered_address

    @property
    def address_string(self):
        return sanitize_string(str(self.address or ""), length=100)

    @property
    def credit_period(self):
        return self.credit_settings.credit_period if self.credit_settings else None

    @property
    def credit_limit(self):
        return self.credit_settings.credit_amount if self.credit_settings else None
