from ths_common.value_objects import PhoneNumber

from finance_erp.domain.company_profile.constants import InvoiceDispatchOption


class CommunicationSettings:
    def __init__(
        self,
        invoice_dispatch_option_on_checkout=None,
        invoice_dispatch_option_with_stay_summary=None,
        should_dispatch_booking_request_with_stay_summary=None,
    ):
        self.invoice_dispatch_option_on_checkout = (
            invoice_dispatch_option_on_checkout or InvoiceDispatchOption.DISABLED
        )
        self.invoice_dispatch_option_with_stay_summary = (
            invoice_dispatch_option_with_stay_summary or InvoiceDispatchOption.DISABLED
        )
        self.should_dispatch_booking_request_with_stay_summary = (
            should_dispatch_booking_request_with_stay_summary or False
        )

    def to_dict(self):
        return {
            "invoice_dispatch_option_on_checkout": self.invoice_dispatch_option_on_checkout,
            "invoice_dispatch_option_with_stay_summary": self.invoice_dispatch_option_with_stay_summary,
            "should_dispatch_booking_request_with_stay_summary": self.should_dispatch_booking_request_with_stay_summary,
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            invoice_dispatch_option_on_checkout=data.get(
                "invoice_dispatch_option_on_checkout"
            ),
            invoice_dispatch_option_with_stay_summary=data.get(
                "invoice_dispatch_option_with_stay_summary"
            ),
            should_dispatch_booking_request_with_stay_summary=data.get(
                "should_dispatch_booking_request_with_stay_summary"
            ),
        )

    @staticmethod
    def _should_dispatch(option):
        return InvoiceDispatchOption.is_enabled(option)

    def _invoice_types_to_dispatch(self, option):
        if not self._should_dispatch(option):
            return None
        return InvoiceDispatchOption.invoice_types(option)

    def should_dispatch_invoice_on_checkout(self):
        return self._should_dispatch(self.invoice_dispatch_option_on_checkout)

    def should_dispatch_stay_summary(self):
        return self._should_dispatch(self.invoice_dispatch_option_with_stay_summary)

    def invoice_types_to_dispatch_on_checkout(self):
        return self._invoice_types_to_dispatch(self.invoice_dispatch_option_on_checkout)

    def invoice_types_to_dispatch_with_stay_summary(self):
        return self._invoice_types_to_dispatch(
            self.invoice_dispatch_option_with_stay_summary
        )


class CorporatePOC:
    def __init__(
        self,
        contact_types,
        department,
        designation,
        email_ids,
        name,
        phone_number,
        poc_type,
        user_id,
        **kwargs,
    ):
        self.contact_types = contact_types
        self.department = department
        self.designation = designation
        self.email_ids = email_ids
        self.name = name
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None
        self.poc_type = poc_type
        self.user_id = user_id
        self.source = kwargs.get("source", None)

    @classmethod
    def from_dict(cls, data):
        return cls(
            contact_types=data.get("contact_types"),
            department=data.get("department"),
            designation=data.get("designation"),
            email_ids=data.get("email_ids"),
            name=data.get("name"),
            phone_number=data.get("phone_number"),
            poc_type=data.get("poc_type"),
            user_id=data.get("user_id"),
            source=data.get("source"),
        )

    def to_dict(self):
        return {
            "contact_types": self.contact_types,
            "department": self.department,
            "designation": self.designation,
            "email_ids": self.email_ids,
            "name": self.name,
            "phone_number": self.phone_number.to_json() if self.phone_number else None,
            "poc_type": self.poc_type,
            "user_id": self.user_id,
            "source": self.source,
        }
