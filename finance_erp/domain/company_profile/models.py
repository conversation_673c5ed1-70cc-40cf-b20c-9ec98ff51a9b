from typing import Dict

from sqlalchemy import (
    DATE,
    JSON,
    Boolean,
    Column,
    DateTime,
    Index,
    Integer,
    String,
    Text,
)
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class CorporateModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "corporate"

    corporate_code = Column(String(50), primary_key=True, nullable=False)
    customer_legal_name = Column(String(255), nullable=False)
    customer_trading_name = Column(String(255))
    address = Column(Text)
    city = Column(String(50))
    phone_number = Column(String(20))
    post_code = Column(String(20))
    email = Column(String(255))
    credit_limit = Column(String(50))
    credit_period = Column(Integer)
    country_code = Column(String(10))
    pan = Column(String(50))
    state_code = Column(String(10))
    gstin = Column(String(50))
    gst_customer_type = Column(String(50))
    tan_number = Column(String(50))
    billing_period = Column(Integer)
    communication_settings = Column(JSON)
    corporate_pocs = Column(JSON)
    next_billing_date = Column(DATE)
    is_billing_enabled = Column(Boolean, default=False)
    external_account_number = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    __table_arg__ = (
        Index("ix_corporate_status", "status"),
        Index("ix_corporate_corporate_code", "corporate_code"),
        Index("ix_corporate_for_billing", "is_billing_enabled", "next_billing_date"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "customer_legal_name": self.customer_legal_name,
            "corporate_code": self.corporate_code,
            "customer_trading_name": self.customer_trading_name,
            "address": self.address,
            "city": self.city,
            "phone_number": self.phone_number,
            "post_code": self.post_code,
            "email": self.email,
            "credit_limit": self.credit_limit,
            "country_code": self.country_code,
            "pan": self.pan,
            "state_code": self.state_code,
            "gstin": self.gstin,
            "gst_customer_type": self.gst_customer_type,
            "tan_number": self.tan_number,
            "billing_period": self.billing_period,
            "communication_settings": self.communication_settings,
            "corporate_pocs": self.corporate_pocs,
            "next_billing_date": self.next_billing_date,
            "external_account_number": self.external_account_number,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
