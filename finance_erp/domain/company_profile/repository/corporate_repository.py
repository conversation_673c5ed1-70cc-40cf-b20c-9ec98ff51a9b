from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.company_profile.adaptors.corporate_adaptor import (
    CorporateAdaptor,
)
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class CorporateReportRepository(BaseRepository):
    corporate_adaptor = CorporateAdaptor()

    def to_entity(self, model):
        return self.corporate_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.corporate_adaptor.to_db_entity(entity)

    def save(self, entity: CorporateEntity) -> CorporateEntity:
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def update(self, entity: CorporateEntity) -> CorporateEntity:
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def bulk_update_records(self, corporate_list: List[CorporateEntity]):
        self.bulk_update_entities(
            CorporateModel,
            corporate_list,
        )
        self.flush_session()

    def insert_many(self, corporate_list: List[CorporateEntity]):
        self._bulk_insert_mappings(
            CorporateModel,
            [
                self.from_entity(corporate).mapping_dict()
                for corporate in corporate_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(CorporateModel, corporate_code=uu_id)
        return self.to_entity(db_model) if db_model else None

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            CorporateModel,
            CorporateModel.corporate_code.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_corporate_ids_for_billing(self, billing_date):
        query = self.query(CorporateModel.corporate_code).filter(
            CorporateModel.next_billing_date == billing_date,
            CorporateModel.is_billing_enabled.is_(True),
            CorporateModel.deleted.is_(False),
        )
        return [db_model[0] for db_model in query.all()]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(CorporateModel)
            .filter(
                CorporateModel.modified_at >= from_date_gte,
                CorporateModel.modified_at <= to_date_lte,
                CorporateModel.deleted == False,
            )
            .order_by(CorporateModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(CorporateModel, [record])
        self.flush_session()

    def get_record_for_update(self, corporate_code, nowait=False):
        model = self.get_for_update(
            CorporateModel, corporate_code=corporate_code, nowait=nowait
        )
        return self.to_entity(model) if model else None

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(CorporateModel)
            .filter(
                CorporateModel.verified == True,
                CorporateModel.status.in_(
                    ERPEntityStatus.allowed_status_for_data_push()
                ),
                CorporateModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )
        return [self.to_entity(db_model) for db_model in db_models]
