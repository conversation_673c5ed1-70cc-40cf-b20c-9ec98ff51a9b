from finance_erp.common.constants import ProcessNames
from finance_erp.domain.shared_kernel.audit.process_level.audit_repository import (
    ProcessLevelAuditRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.entity import (
    ProcessLevelAudit,
)
from object_registry import register_instance


class DataPushAuditEvents:
    INCOMING_REQUEST_EVENT = "incoming_request_event"
    RETRY_EVENT = "retry_event"
    QUEUING_EVENT = "queuing_event"
    PROCESS_BEGIN_EVENT = "process_begin_event"
    FAILURE_EVENT = "failure_event"
    SUCCESS_EVENT = "success_event"


@register_instance(dependencies=[ProcessLevelAuditRepository])
class NavPushAuditService:
    def __init__(self, audit_repository: ProcessLevelAuditRepository):
        self.audit_repository = audit_repository

    @staticmethod
    def create_audit_object(
        event_type,
        event_id,
        report_type,
        failure_message=None,
        error_trace=None,
        stats: dict = None,
    ):
        return ProcessLevelAudit(
            audit_type=ProcessNames.NAVISION_PUSH,
            entity_type=report_type,
            event_id=event_id,
            event_type=event_type,
            event_data=stats,
            description=failure_message,
            detailed_trace=error_trace,
        )

    def record_incoming_data_push_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.INCOMING_REQUEST_EVENT,
            event_id,
            report_type,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_data_push_retry_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.RETRY_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_data_push_queued_event(
        self, event_id, report_type, stats: dict = None, **kwargs
    ):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.QUEUING_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_data_push_started_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.PROCESS_BEGIN_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_data_push_failure_event(
        self, event_id, report_type, failure_message, error_trace, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.FAILURE_EVENT,
            event_id,
            report_type,
            failure_message,
            error_trace,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_data_push_completed_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            DataPushAuditEvents.SUCCESS_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)
