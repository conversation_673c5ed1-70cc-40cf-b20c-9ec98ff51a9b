from finance_erp.domain.shared_kernel.audit.process_level.adaptor import (
    ProcessLevelAuditAdaptor,
)
from finance_erp.domain.shared_kernel.audit.process_level.entity import (
    ProcessLevelAudit,
)
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class ProcessLevelAuditRepository(BaseRepository):
    adaptor = ProcessLevelAuditAdaptor()

    def to_entity(self, model):
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adaptor.to_db_entity(entity)

    def create(self, entity: ProcessLevelAudit):
        self._save(self.from_entity(entity))
