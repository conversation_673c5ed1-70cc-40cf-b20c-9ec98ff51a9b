import json

from finance_erp.domain.shared_kernel.audit.models import ProcessLevelAuditModel
from finance_erp.domain.shared_kernel.audit.process_level.entity import (
    ProcessLevelAudit,
)
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class SetEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, set):
            return list(obj)
        return json.JSONEncoder.default(self, obj)


class ProcessLevelAuditAdaptor(BaseAdaptor):
    def to_db_entity(
        self, domain_entity: ProcessLevelAudit, **kwargs
    ) -> ProcessLevelAuditModel:
        # noinspection PyArgumentList
        return ProcessLevelAuditModel(
            audit_type=domain_entity.audit_type,
            entity_type=domain_entity.entity_type,
            event_type=domain_entity.event_type,
            event_id=domain_entity.event_id,
            event_data=json.loads(json.dumps(domain_entity.event_data, cls=SetEncoder))
            if domain_entity.event_data
            else None,
            description=domain_entity.description,
            detailed_trace=domain_entity.detailed_trace,
            created_at=domain_entity.created_at,
        )

    def to_domain_entity(
        self, db_entity: ProcessLevelAuditModel, **kwargs
    ) -> ProcessLevelAudit:
        return ProcessLevelAudit(
            audit_type=db_entity.audit_type,
            entity_type=db_entity.entity_type,
            event_type=db_entity.event_type,
            event_id=db_entity.event_id,
            event_data=db_entity.event_data,
            description=db_entity.description,
            detailed_trace=db_entity.detailed_trace,
            created_at=db_entity.created_at,
        )
