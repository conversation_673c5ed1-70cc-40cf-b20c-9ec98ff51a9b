from datetime import datetime
from typing import Optional

from pydantic import BaseModel

from finance_erp.domain.shared_kernel.audit.communication_audit.value_objects import (
    AbstractCommunicationDetails,
)


class CommunicationLog(BaseModel):
    resource_type: str
    resource_id: str
    communication_type: str
    status: str
    communication_details: Optional[AbstractCommunicationDetails] = None
    notification_id: Optional[str] = None
    message: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def update(
        self, status, communication_details=None, notification_id=None, message=None
    ):
        self.status = status
        self.communication_details = communication_details
        self.notification_id = notification_id
        self.message = message

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
