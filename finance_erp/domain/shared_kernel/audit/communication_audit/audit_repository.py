from finance_erp.domain.shared_kernel.audit.communication_audit.adaptor import (
    CommunicationLogAdaptor,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.entity import (
    CommunicationLog,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.models import (
    CommunicationLogModel,
)
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class CommunicationAuditRepository(BaseRepository):
    adaptor = CommunicationLogAdaptor()
    model = CommunicationLogModel

    def to_entity(self, model):
        return self.adaptor.to_entity(model)

    def from_entity(self, entity):
        return self.adaptor.from_entity(entity)

    def create(self, entity: CommunicationLog):
        self._save(self.from_entity(entity))

    def update(self, entity: CommunicationLog):
        self._update(self.from_entity(entity))

    def _existing_communication_query(
        self,
        resource_name,
        resource_id,
        communication_type,
        status=None,
    ):
        query = self.query(CommunicationLogModel).filter(
            self.model.resource_type == resource_name,
            self.model.resource_id == resource_id,
            self.model.communication_type == communication_type,
        )
        if status:
            query = query.filter(self.model.status == status)
        return query

    def fetch(self, resource_name, resource_id, communication_type, status=None):
        query = self.query(CommunicationLogModel).filter(
            self.model.resource_type == resource_name,
            self.model.resource_id == resource_id,
            self.model.communication_type == communication_type,
        )
        if status:
            query = query.filter(self.model.status == status)
        communication = query.first()
        return self.to_entity(communication) if communication else None
