from typing import Type

from finance_erp.domain.shared_kernel.audit.communication_audit.entity import (
    CommunicationLog,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.models import (
    CommunicationLogModel,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.value_objects import (
    COMMUNICATION_DETAILS_MAPPER,
    AbstractCommunicationDetails,
)


class CommunicationLogAdaptor:
    @staticmethod
    def from_entity(communication_log: CommunicationLog) -> CommunicationLogModel:
        # noinspection PyArgumentList
        return CommunicationLogModel(
            id=communication_log.id,
            resource_type=communication_log.resource_type,
            resource_id=communication_log.resource_id,
            communication_type=communication_log.communication_type,
            status=communication_log.status,
            communication_details=communication_log.communication_details.to_dict(
                obj=communication_log.communication_details
            )
            if communication_log.communication_details
            else None,
            notification_id=communication_log.notification_id,
            message=communication_log.message,
        )

    @staticmethod
    def to_entity(model: CommunicationLogModel) -> CommunicationLog:
        communication_details_class: Type[
            AbstractCommunicationDetails
        ] = COMMUNICATION_DETAILS_MAPPER[model.communication_type]
        return CommunicationLog(
            id=model.id,
            resource_type=model.resource_type,
            resource_id=model.resource_id,
            communication_type=model.communication_type,
            status=model.status,
            communication_details=communication_details_class.from_dict(
                model.communication_details
            )
            if model.communication_details
            else None,
            notification_id=model.notification_id,
            message=model.message,
        )
