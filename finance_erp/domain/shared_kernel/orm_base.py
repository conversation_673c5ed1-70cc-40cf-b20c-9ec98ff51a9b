from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime


class TimeStampMixin(object):
    """
    model for created time and modified time
    """

    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)
    modified_at = Column(
        DateTime(timezone=True),
        default=datetime.now,
        onupdate=datetime.now,
        nullable=False,
    )


class DeleteMixin(object):
    deleted = Column("deleted", Boolean, default=False)
