from typing import List, Optional, Union

from finance_erp.domain.back_office.constants import GLGroupC<PERSON>, GLRevenueTypes
from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.entity.charge_entity import ChargeEntity
from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.entity.ledger_controls import (
    ErrorFileItem,
    GLLedgerItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.value_objects import TaxDetails
from object_registry import register_instance


@register_instance()
class ChargeProcessor:
    def __init__(self):
        pass

    def process(
        self,
        date,
        charge_data: List[ChargeEntity],
        allowance_data: List[AllowanceEntity],
        config_master: ConfigMaster,
    ) -> LedgerControls:
        ledger_controls = LedgerControls()
        self._process_entries(
            date,
            charge_data,
            GLRevenueTypes.CHARGE,
            GLRevenueTypes.TAX_ON_CHARGE,
            config_master,
            ledger_controls,
        )
        self._process_entries(
            date,
            allowance_data,
            GLRevenueTypes.ALLOWANCE,
            GLRevenueTypes.TAX_ON_ALLOWANCE,
            config_master,
            ledger_controls,
            is_allowance=True,
        )
        return ledger_controls

    def _process_entries(
        self,
        date,
        entries: Union[List[ChargeEntity], List[AllowanceEntity]],
        txn_type,
        tax_txn_type,
        config_master,
        ledger_controls,
        is_allowance=False,
    ):
        for entry in entries:
            txn_master = self._get_txn_master(entry, txn_type, config_master)
            if txn_master and txn_master.gl_code:
                ledger_controls.gl_controls.append(
                    self._build_gl_control(
                        date, entry.pretax_amount, txn_master, is_allowance
                    )
                )
            else:
                self._capture_error_on_charges_and_allowance(
                    ledger_controls,
                    entry,
                    txn_type,
                    gl_code_missing=txn_master and not txn_master.gl_code,
                )
            for tax in entry.tax_details or []:
                tax_amount = tax.amount
                if not tax_amount:
                    continue
                tax_txn_master = self._get_txn_master(
                    entry, tax_txn_type, config_master, tax
                )
                if not tax_txn_master and isinstance(entry, AllowanceEntity):
                    tax_txn_master = self._get_txn_master(
                        entry,
                        tax_txn_type,
                        config_master,
                        entry.get_tax_item_with_restored_tax_percentage(tax),
                    )

                if tax_txn_master and tax_txn_master.gl_code:
                    ledger_controls.gl_controls.append(
                        self._build_gl_control(
                            date, tax_amount, tax_txn_master, is_allowance
                        )
                    )
                else:
                    self._capture_error_on_taxes(
                        ledger_controls,
                        entry,
                        tax,
                        tax_txn_type,
                        txn_type,
                        gl_code_missing=txn_master and not txn_master.gl_code,
                    )

        return ledger_controls

    @staticmethod
    def _get_txn_master(
        entry: Union[ChargeEntity, AllowanceEntity],
        txn_type,
        config_master,
        tax: Optional[TaxDetails] = None,
    ) -> Optional[TransactionMasterEntity]:
        try:
            return config_master.get_txn_master(
                txn_type=txn_type,
                identifier=entry.sku_id,
                revenue_center=entry.revenue_center,
                tax_percentage=tax and tax.percentage,
                tax_type=tax and tax.tax_type,
            )
        except KeyError:
            return None

    @staticmethod
    def _build_gl_control(
        date,
        amount,
        txn_master: TransactionMasterEntity,
        is_allowance=False,
    ):
        # charges are outflow in GL1 flow so +ve amount
        amount = -amount if is_allowance else amount
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        return GLLedgerItem(
            posting_date=date,
            hotel_id=txn_master.hotel_id,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            credit=credit,
            debit=debit,
            particular=txn_master.particulars,
            is_mergeable=txn_master.merge_gl_entries,
            txn_amount=amount,
            txn_group=GLGroupCodes.GUEST_GROUP_CODE,
        )

    @staticmethod
    def _capture_error_on_taxes(
        ledger_controls,
        entry: Union[ChargeEntity, AllowanceEntity],
        tax,
        tax_txn_type,
        txn_type,
        gl_code_missing=False,
    ):
        error_file_reason = (
            f"due to missing {'txn master' if not gl_code_missing else 'GLCode'}"
        )
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {tax_txn_type}",
                identifier=entry.sku_id,
                identifier_type="sku_id",
                reason=error_file_reason,
                other_details=f"({tax.tax_type}: {tax.percentage}) - {entry.bill_id} - {tax.amount} in {entry.revenue_center}",
            )
        )

    @staticmethod
    def _capture_error_on_charges_and_allowance(
        ledger_controls,
        entry: ChargeEntity,
        txn_type,
        gl_code_missing=False,
    ):
        error_file_reason = (
            f"due to missing {'txn master' if not gl_code_missing else 'GLCode'}"
        )
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {txn_type}",
                identifier=entry.sku_id,
                identifier_type="sku_id",
                reason=error_file_reason,
                other_details=f"{entry.bill_id} - {entry.pretax_amount} in {entry.revenue_center}",
            )
        )
