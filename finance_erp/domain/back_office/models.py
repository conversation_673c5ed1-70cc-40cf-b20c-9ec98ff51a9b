from typing import Any, Dict

from sqlalchemy import Boolean, Column, Date, Float, Index, Integer, String
from sqlalchemy.dialects.postgresql import JSONB
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class BaseModel(Base, TimeStampMixin, DeleteMixin):
    __abstract__ = True

    uu_id = Column(String, primary_key=True)
    bill_id = Column(String, nullable=False)
    deleted = Column(Boolean, default=False)

    def mapping_dict(self) -> Dict:
        return {
            "uu_id": self.uu_id,
            "bill_id": self.bill_id,
            "deleted": self.deleted,
        }


class TransactionMaster(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "transaction_master"

    transaction_id = Column(Integer, primary_key=True)
    hotel_id = Column(String, nullable=True)
    erp_name = Column(String, nullable=False)
    identifier_name = Column(String, nullable=False)
    display_name = Column(String, nullable=False)
    gl_code = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    merge_gl_entries = Column(Boolean, default=True)
    particulars = Column(String)
    revenue_center = Column(String)
    identifier = Column(String, nullable=False)  # sku_id/payment_mode
    transaction_type = Column(String, nullable=False)  # charge/allowance/tax/payment
    transaction_metadata = Column(JSONB, nullable=True)

    def mapping_dict(self) -> Dict:
        return {
            "transaction_id": self.transaction_id,
            "hotel_id": self.hotel_id,
            "erp_name": self.erp_name,
            "gl_code": self.gl_code,
            "is_active": self.is_active,
            "merge_gl_entries": self.merge_gl_entries,
            "particulars": self.particulars,
            "identifier_name": self.identifier_name,
            "display_name": self.display_name,
            "revenue_center": self.revenue_center,
            "identifier": self.identifier,
            "transaction_type": self.transaction_type,
            "transaction_metadata": self.transaction_metadata,
        }


class GLLedgerItemModel(Base, DeleteMixin, TimeStampMixin):
    __tablename__ = "gl_ledger_item"

    id = Column(Integer, primary_key=True, autoincrement=True)
    posting_date = Column(Date, nullable=False)
    hotel_id = Column(String, nullable=False)
    erp_name = Column(String, nullable=False)
    particular = Column(String)
    txn_amount = Column(Float)
    txn_group = Column(Integer)
    gl_code = Column(String)
    txn_id = Column(String)
    debit = Column(Float)
    credit = Column(Float)

    __table_args__ = (
        Index("idx_gl_ledger_item_hotel_id", "hotel_id"),
        Index("idx_gl_ledger_item_posting_date", "posting_date"),
        Index(
            "idx_gl_ledger_item_hotel_id_posting_date_erp_name",
            "hotel_id",
            "posting_date",
            "erp_name",
        ),
    )

    def mapping_dict(self) -> Dict[str, Any]:
        return {
            "posting_date": self.posting_date,
            "hotel_id": self.hotel_id,
            "erp_name": self.erp_name,
            "particular": self.particular,
            "txn_amount": self.txn_amount,
            "txn_group": self.txn_group,
            "gl_code": self.gl_code,
            "txn_id": self.txn_id,
            "debit": self.debit,
            "credit": self.credit,
            "deleted": self.deleted,
            "id": self.id,
        }


class CLLedgerItemModel(Base, DeleteMixin, TimeStampMixin):
    __tablename__ = "cl_ledger_item"

    id = Column(Integer, primary_key=True, autoincrement=True)
    account_number = Column(String, nullable=False)
    account_name = Column(String)
    guest_name = Column(String)
    posting_date = Column(Date, nullable=False)
    hotel_id = Column(String, nullable=False)
    erp_name = Column(String, nullable=False)
    payment_type = Column(String)
    txt_date = Column(Date)
    checkin_date = Column(Date)
    checkout_date = Column(Date)
    txn_amount = Column(Float)
    booking_id = Column(String)
    folio_number = Column(Integer)
    room_number = Column(String)

    def mapping_dict(self) -> Dict[str, Any]:
        return {
            "account_number": self.account_number,
            "account_name": self.account_name,
            "guest_name": self.guest_name,
            "posting_date": self.posting_date,
            "txt_date": self.txt_date,
            "hotel_id": self.hotel_id,
            "erp_name": self.erp_name,
            "payment_type": self.payment_type,
            "checkin_date": self.checkin_date,
            "checkout_date": self.checkout_date,
            "txn_amount": self.txn_amount,
            "folio_number": self.folio_number,
            "booking_id": self.booking_id,
            "room_number": self.room_number,
            "deleted": self.deleted,
            "id": self.id,
        }

    __table_args__ = (
        Index("idx_cl_ledger_item_hotel_id", "hotel_id"),
        Index("idx_cl_ledger_item_posting_date", "posting_date"),
        Index(
            "idx_cl_ledger_item_hotel_id_posting_date_erp_name",
            "hotel_id",
            "posting_date",
            "erp_name",
        ),
    )


class LedgersFileRecord(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "ledgers_file_record"

    id = Column(Integer, primary_key=True, autoincrement=True)
    hotel_id = Column(String, nullable=False)
    erp_name = Column(String, nullable=False)
    business_date = Column(Date, nullable=False)
    ledger_file_type = Column(String)
    ledger_file_path = Column(String)
    ledger_file_name = Column(String)
    status = Column(String)
    remarks = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "hotel_id": self.hotel_id,
            "erp_name": self.erp_name,
            "business_date": self.business_date,
            "ledger_file_type": self.ledger_file_type,
            "ledger_file_path": self.ledger_file_path,
            "ledger_file_name": self.ledger_file_name,
            "status": self.status,
            "remarks": self.remarks,
            "deleted": self.deleted,
            "id": self.id,
        }


class PaymentModel(BaseModel):
    __tablename__ = "back_office_payment_details"

    payment_id = Column(Integer, nullable=False)
    payment_split_id = Column(Integer)
    payment_type = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    posting_date = Column(Date, nullable=False)
    date_of_payment = Column(Date, nullable=False)
    payment_mode = Column(String, nullable=False)
    crs_payment_mode = Column(String, nullable=False)
    crs_payment_mode_sub_type = Column(String, nullable=True)
    payment_mode_sub_type = Column(String)
    payment_ref_id = Column(String)
    payment_channel = Column(String)
    hotel_id = Column(String, nullable=False)
    revenue_center = Column(String)
    booking_id = Column(String)
    category = Column(String)
    owner_name = Column(String)
    folio_number = Column(Integer, nullable=True)
    debtor_code = Column(String)
    checkin_date = Column(Date, nullable=True)
    checkout_date = Column(Date, nullable=True)
    room_number = Column(String, nullable=True)
    billed_entity_id = Column(Integer, nullable=True)
    account_number = Column(Integer, nullable=True)
    booking_reference_number = Column(String, nullable=True)
    fin_erp_posting_date = Column(Date, nullable=True)

    def mapping_dict(self) -> Dict:
        parent_dict = super().mapping_dict()
        return {
            **parent_dict,
            "payment_id": self.payment_id,
            "payment_split_id": self.payment_split_id,
            "payment_type": self.payment_type,
            "amount": self.amount,
            "posting_date": self.posting_date,
            "date_of_payment": self.date_of_payment,
            "payment_mode": self.payment_mode,
            "crs_payment_mode": self.crs_payment_mode,
            "payment_mode_sub_type": self.payment_mode_sub_type,
            "crs_payment_mode_sub_type": self.crs_payment_mode_sub_type,
            "payment_ref_id": self.payment_ref_id,
            "payment_channel": self.payment_channel,
            "hotel_id": self.hotel_id,
            "revenue_center": self.revenue_center,
            "booking_id": self.booking_id,
            "category": self.category,
            "owner_name": self.owner_name,
            "folio_number": self.folio_number,
            "debtor_code": self.debtor_code,
            "checkin_date": self.checkin_date,
            "checkout_date": self.checkout_date,
            "room_number": self.room_number,
            "billed_entity_id": self.billed_entity_id,
            "account_number": self.account_number,
            "booking_reference_number": self.booking_reference_number,
            "fin_erp_posting_date": self.fin_erp_posting_date,
        }


class ChargeModel(BaseModel):
    __tablename__ = "back_office_charge_details"

    charge_split_id = Column(Integer)
    charge_id = Column(Integer, nullable=False)
    pretax_amount = Column(Float, nullable=False)
    tax_amount = Column(Float, nullable=False)
    posttax_amount = Column(Float, nullable=False)
    tax_details = Column(JSONB)
    charge_type = Column(String, nullable=False)
    bill_to_type = Column(String)
    item_id = Column(String)
    sku_category_id = Column(String)
    applicable_business_date = Column(Date)
    posting_date = Column(Date, nullable=False)
    is_inclusion_charge = Column(Boolean, default=False)
    owner_name = Column(String)
    category = Column(String)
    folio_number = Column(Integer, nullable=True)
    hotel_id = Column(String, nullable=False)
    revenue_center = Column(String)
    booking_id = Column(String)
    billed_entity_id = Column(Integer, nullable=True)
    account_number = Column(Integer, nullable=True)
    booking_reference_number = Column(String, nullable=True)
    fin_erp_posting_date = Column(Date, nullable=True)

    def mapping_dict(self) -> Dict:
        parent_dict = super().mapping_dict()
        return {
            **parent_dict,
            "charge_split_id": self.charge_split_id,
            "charge_id": self.charge_id,
            "pretax_amount": self.pretax_amount,
            "posttax_amount": self.posttax_amount,
            "tax_amount": self.tax_amount,
            "tax_details": self.tax_details,
            "charge_type": self.charge_type,
            "bill_to_type": self.bill_to_type,
            "item_id": self.item_id,
            "sku_category_id": self.sku_category_id,
            "applicable_business_date": self.applicable_business_date,
            "posting_date": self.posting_date,
            "is_inclusion_charge": self.is_inclusion_charge,
            "category": self.category,
            "owner_name": self.owner_name,
            "folio_number": self.folio_number,
            "hotel_id": self.hotel_id,
            "revenue_center": self.revenue_center,
            "booking_id": self.booking_id,
            "billed_entity_id": self.billed_entity_id,
            "account_number": self.account_number,
            "booking_reference_number": self.booking_reference_number,
            "fin_erp_posting_date": self.fin_erp_posting_date,
        }


class AllowanceModel(BaseModel):
    __tablename__ = "back_office_allowance_details"

    allowance_id = Column(Integer, nullable=False)
    charge_split_id = Column(Integer)
    charge_id = Column(Integer, nullable=False)
    posting_date = Column(Date, nullable=False)
    tax_amount = Column(Float, nullable=False)
    posttax_amount = Column(Float, nullable=False)
    pretax_amount = Column(Float, nullable=False)
    tax_details = Column(JSONB)
    charge_type = Column(String)
    bill_to_type = Column(String)
    item_id = Column(String)
    sku_category_id = Column(String)
    hotel_id = Column(String, nullable=False)
    revenue_center = Column(String)
    booking_id = Column(String)
    owner_name = Column(String)
    category = Column(String)
    folio_number = Column(Integer, nullable=True)
    billed_entity_id = Column(Integer, nullable=True)
    account_number = Column(Integer, nullable=True)
    booking_reference_number = Column(String, nullable=True)
    fin_erp_posting_date = Column(Date, nullable=True)

    def mapping_dict(self) -> Dict:
        parent_dict = super().mapping_dict()
        return {
            **parent_dict,
            "allowance_id": self.allowance_id,
            "charge_split_id": self.charge_split_id,
            "charge_id": self.charge_id,
            "posting_date": self.posting_date,
            "tax_amount": self.tax_amount,
            "posttax_amount": self.posttax_amount,
            "pretax_amount": self.pretax_amount,
            "tax_details": self.tax_details,
            "charge_type": self.charge_type,
            "bill_to_type": self.bill_to_type,
            "item_id": self.item_id,
            "sku_category_id": self.sku_category_id,
            "hotel_id": self.hotel_id,
            "revenue_center": self.revenue_center,
            "booking_id": self.booking_id,
            "owner_name": self.owner_name,
            "category": self.category,
            "folio_number": self.folio_number,
            "billed_entity_id": self.billed_entity_id,
            "account_number": self.account_number,
            "booking_reference_number": self.booking_reference_number,
            "fin_erp_posting_date": self.fin_erp_posting_date,
        }


class FolioDetailsModel(BaseModel):
    __tablename__ = "back_office_folio_details"

    hotel_id = Column(String)
    bill_id = Column(String, nullable=True)
    booking_id = Column(String, nullable=True)
    category = Column(String, nullable=True)
    owner_name = Column(String, nullable=True)
    folio_number = Column(Integer, nullable=True)
    billed_entity_id = Column(Integer, nullable=True)
    account_number = Column(Integer, nullable=True)
    booking_reference_number = Column(String, nullable=True)
    is_credit_folio = Column(String, nullable=True)
    payment_split_details = Column(JSONB, nullable=True)
    folio_status = Column(String, nullable=True)
    fin_erp_posting_date = Column(Date, nullable=True)

    def mapping_dict(self) -> Dict:
        parent_dict = super().mapping_dict()
        return {
            **parent_dict,
            "hotel_id": self.hotel_id,
            "booking_id": self.booking_id,
            "category": self.category,
            "owner_name": self.owner_name,
            "folio_status": self.folio_status,
            "folio_number": self.folio_number,
            "billed_entity_id": self.billed_entity_id,
            "account_number": self.account_number,
            "booking_reference_number": self.booking_reference_number,
            "is_credit_folio": self.is_credit_folio,
            "payment_split_details": self.payment_split_details,
            "fin_erp_posting_date": self.fin_erp_posting_date,
        }
