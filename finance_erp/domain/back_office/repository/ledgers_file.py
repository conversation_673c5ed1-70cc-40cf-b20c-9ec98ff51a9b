from typing import List

from finance_erp.domain.back_office.adaptors.ledgers_file_adaptor import (
    LedgersFileAdaptor,
)
from finance_erp.domain.back_office.entity.ledgers_file import LedgersFileEntity
from finance_erp.domain.back_office.models import LedgersFileRecord
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class LedgersFileRepository(BaseRepository):
    ledgers_file_adaptor = LedgersFileAdaptor()

    def to_entity(self, model):
        return self.ledgers_file_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.ledgers_file_adaptor.to_db_entity(entity)

    def save(self, entity: LedgersFileEntity) -> LedgersFileEntity:
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def insert_many(self, ledger_files: List[LedgersFileEntity]):
        self._bulk_insert_mappings(
            LedgersFileRecord,
            [
                self.from_entity(ledger_file).mapping_dict()
                for ledger_file in ledger_files
            ],
        )
        self.flush_session()

    def update(self, entity: LedgersFileEntity) -> LedgersFileEntity:
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def get(self, hotel_id, business_date):
        db_model = super().get_one(
            LedgersFileRecord, hotel_id=hotel_id, business_date=business_date
        )
        return self.to_entity(db_model) if db_model else None

    def get_file_details_for_date_range(
        self, hotel_id, start_date, end_date, erp_name=None
    ):
        query = self.query(LedgersFileRecord).filter(
            LedgersFileRecord.business_date >= start_date,
            LedgersFileRecord.business_date <= end_date,
            LedgersFileRecord.hotel_id == hotel_id,
            LedgersFileRecord.deleted.is_(False),
        )
        if erp_name:
            query = query.filter(LedgersFileRecord.erp_name == erp_name)
        query = query.order_by(LedgersFileRecord.business_date.desc())

        return [self.to_entity(db_model) for db_model in query.all()]

    def update_many(self, records):
        self._bulk_update_mappings(
            LedgersFileRecord,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        business_date=None,
        erp_name=None,
        limit: int = None,
        offset: int = None,
    ):
        query = self.query(LedgersFileRecord).filter(
            LedgersFileRecord.deleted.is_(False)
        )

        if hotel_id:
            query = query.filter(LedgersFileRecord.hotel_id == hotel_id)

        if business_date:
            query = query.filter(LedgersFileRecord.business_date == business_date)

        if erp_name:
            query = query.filter(LedgersFileRecord.erp_name == erp_name)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]
