from typing import List

from finance_erp.domain.back_office.adaptors.ledger_control_adaptors import (
    CLLedgerItemAdaptor,
)
from finance_erp.domain.back_office.entity.ledger_controls import CLLedgerItem
from finance_erp.domain.back_office.models import CLLedgerItemModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class CLControlRepository(BaseRepository):
    adaptor = CLLedgerItemAdaptor()

    def to_entity(self, model):
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adaptor.to_db_entity(entity)

    def insert_many(self, cl_items: List[CLLedgerItem]):
        self._bulk_insert_mappings(
            CLLedgerItemModel,
            [self.from_entity(cl_item).mapping_dict() for cl_item in cl_items],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            CLLedgerItemModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        erp_name=None,
        limit: int = None,
        offset: int = None,
    ) -> List[CLLedgerItem]:
        query = self.query(CLLedgerItemModel).filter(
            CLLedgerItemModel.deleted.is_(False)
        )

        if hotel_id:
            query = query.filter(CLLedgerItemModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(CLLedgerItemModel.posting_date == posting_date)

        if erp_name:
            query = query.filter(CLLedgerItemModel.erp_name == erp_name)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]
