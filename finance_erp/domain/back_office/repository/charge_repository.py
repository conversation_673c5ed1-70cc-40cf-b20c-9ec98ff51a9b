from typing import List, Optional, Tuple

from sqlalchemy import tuple_

from finance_erp.domain.back_office.adaptors.charge_adaptor import ChargeAdaptor
from finance_erp.domain.back_office.entity.charge_entity import ChargeEntity
from finance_erp.domain.back_office.models import ChargeModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class ChargeRepository(BaseRepository):
    charge_adaptor = ChargeAdaptor()

    def to_entity(self, model):
        return self.charge_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.charge_adaptor.to_db_entity(entity)

    def insert_many(self, charge_data_list: List[ChargeEntity]):
        self._bulk_insert_mappings(
            ChargeModel,
            [
                self.from_entity(charge_data).mapping_dict()
                for charge_data in charge_data_list
            ],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            ChargeModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        charge_identifiers: Optional[List[Tuple[str, int]]] = None,
        limit: int = None,
        offset: int = None,
    ) -> List[ChargeEntity]:
        query = self.query(ChargeModel).filter(ChargeModel.deleted.is_(False))

        if hotel_id:
            query = query.filter(ChargeModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(ChargeModel.fin_erp_posting_date == posting_date)

        if charge_identifiers:
            query = query.filter(
                tuple_(ChargeModel.bill_id, ChargeModel.charge_id).in_(
                    charge_identifiers
                )
            )

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]

    def get(self, uu_id):
        db_model = super().get_one(ChargeModel, uu_id=uu_id)
        return self.to_entity(db_model)
