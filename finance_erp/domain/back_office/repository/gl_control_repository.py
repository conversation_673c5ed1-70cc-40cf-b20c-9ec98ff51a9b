from typing import List

from finance_erp.domain.back_office.adaptors.ledger_control_adaptors import (
    GLLedgerItemAdaptor,
)
from finance_erp.domain.back_office.entity.ledger_controls import GLLedgerItem
from finance_erp.domain.back_office.models import GLLedgerItemModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class GLControlRepository(BaseRepository):
    adaptor = GLLedgerItemAdaptor()

    def to_entity(self, model):
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adaptor.to_db_entity(entity)

    def insert_many(self, gl_items: List[GLLedgerItem]):
        self._bulk_insert_mappings(
            GLLedgerItemModel,
            [self.from_entity(gl_item).mapping_dict() for gl_item in gl_items],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            GLLedgerItemModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        erp_name=None,
        limit: int = None,
        offset: int = None,
    ) -> List[GLLedgerItem]:
        query = self.query(GLLedgerItemModel).filter(
            GLLedgerItemModel.deleted.is_(False)
        )

        if hotel_id:
            query = query.filter(GLLedgerItemModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(GLLedgerItemModel.posting_date == posting_date)

        if erp_name:
            query = query.filter(GLLedgerItemModel.erp_name == erp_name)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]
