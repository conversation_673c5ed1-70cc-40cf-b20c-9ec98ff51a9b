from typing import Any, Dict, List

from finance_erp.domain.back_office.adaptors.folio_details_adaptor import (
    FolioDetailsAdaptor,
)
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.models import FolioDetailsModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class FolioDetailsRepository(BaseRepository):
    folio_adaptor = FolioDetailsAdaptor()

    def to_entity(self, model):
        return self.folio_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.folio_adaptor.to_db_entity(entity)

    def insert_many(self, folio_data_list: List[FolioDetailsEntity]):
        self._bulk_insert_mappings(
            FolioDetailsModel,
            [
                self.from_entity(folio_data).mapping_dict()
                for folio_data in folio_data_list
            ],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            FolioDetailsModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        status=None,
        limit: int = None,
        offset: int = None,
    ) -> List[FolioDetailsEntity]:
        query = self.query(FolioDetailsModel).filter(
            FolioDetailsModel.deleted.is_(False)
        )

        if hotel_id:
            query = query.filter(FolioDetailsModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(FolioDetailsModel.fin_erp_posting_date == posting_date)

        if status:
            query = query.filter(FolioDetailsModel.folio_status == status)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]

    def get(self, uu_id):
        db_model = super().get_one(FolioDetailsModel, uu_id=uu_id)
        return self.to_entity(db_model)
