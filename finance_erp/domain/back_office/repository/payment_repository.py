from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import tuple_

from finance_erp.domain.back_office.adaptors.payment_adaptor import PaymentAdaptor
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.models import PaymentModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PaymentRepository(BaseRepository):
    payment_adaptor = PaymentAdaptor()

    def to_entity(self, model):
        return self.payment_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.payment_adaptor.to_db_entity(entity)

    def insert_many(self, payment_data_list: List[PaymentEntity]):
        self._bulk_insert_mappings(
            PaymentModel,
            [
                self.from_entity(payment_data).mapping_dict()
                for payment_data in payment_data_list
            ],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            PaymentModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        bill_id_payment_id: Optional[List[Tuple[str, str]]] = None,
        limit: int = None,
        offset: int = None,
    ) -> List[PaymentEntity]:
        query = self.query(PaymentModel).filter(PaymentModel.deleted.is_(False))

        if hotel_id:
            query = query.filter(PaymentModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(PaymentModel.fin_erp_posting_date == posting_date)

        if bill_id_payment_id:
            query = query.filter(
                tuple_(PaymentModel.bill_id, PaymentModel.payment_id).in_(
                    bill_id_payment_id
                )
            )

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]

    def get(self, uu_id):
        db_model = super().get_one(PaymentModel, uu_id=uu_id)
        return self.to_entity(db_model)
