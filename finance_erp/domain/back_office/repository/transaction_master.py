from typing import List

from finance_erp.domain.back_office.adaptors.transaction_master_adaptor import (
    TransactionMasterAdaptor,
)
from finance_erp.domain.back_office.constants import IntegratedERPs
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.models import TransactionMaster
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class TransactionMasterRepository(BaseRepository):
    transaction_master_adaptor = TransactionMasterAdaptor()

    def to_entity(self, model):
        return self.transaction_master_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.transaction_master_adaptor.to_db_entity(entity)

    def save(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def update(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def insert_many(self, transactions_list: List[TransactionMasterEntity]):
        self._bulk_insert_mappings(
            TransactionMaster,
            [
                self.from_entity(transaction).mapping_dict()
                for transaction in transactions_list
            ],
        )
        self.flush_session()

    def update_many(self, records):
        self.bulk_update_entities(
            TransactionMaster,
            records,
        )
        self.flush_session()

    def create_or_update_records_bulk(self, txn_entries):
        new_records = [txn for txn in txn_entries if txn.is_new]
        dirty_records = [txn for txn in txn_entries if txn.is_dirty]

        if new_records:
            self.insert_many(new_records)

        if dirty_records:
            self.update_many(dirty_records)

    def load_all(
        self, hotel_id: str, erp_name=IntegratedERPs.PROLOGIC, is_active=False
    ) -> List[TransactionMasterEntity]:
        query = self.query(TransactionMaster).filter(
            TransactionMaster.hotel_id == hotel_id,
            TransactionMaster.erp_name == erp_name,
            TransactionMaster.deleted.is_(False),
        )
        if is_active:
            query = query.filter(TransactionMaster.is_active.is_(is_active))
        return [self.to_entity(model) for model in query.all()]
