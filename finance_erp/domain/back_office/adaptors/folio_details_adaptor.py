from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.models import FolioDetailsModel
from finance_erp.domain.back_office.value_objects import PaymentSplitDetails
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class FolioDetailsAdaptor(BaseAdaptor):
    def to_db_entity(
        self, domain_entity: FolioDetailsEntity, **kwargs
    ) -> FolioDetailsModel:
        """Converts a FolioDetailsEntity domain entity into a FolioDetailsModel database entity."""
        return FolioDetailsModel(
            uu_id=domain_entity.uu_id,
            hotel_id=domain_entity.hotel_id,
            bill_id=domain_entity.bill_id,
            booking_id=domain_entity.booking_id,
            category=domain_entity.category,
            owner_name=domain_entity.owner_name,
            folio_status=domain_entity.folio_status,
            folio_number=domain_entity.folio_number,
            billed_entity_id=domain_entity.billed_entity_id,
            account_number=domain_entity.account_number,
            booking_reference_number=domain_entity.booking_reference_number,
            is_credit_folio=domain_entity.is_credit_folio,
            payment_split_details=[
                detail.to_json() for detail in domain_entity.payment_split_details
            ]
            if domain_entity.payment_split_details
            else None,
            fin_erp_posting_date=domain_entity.fin_erp_posting_date,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(
        self, db_entity: FolioDetailsModel, **kwargs
    ) -> FolioDetailsEntity:
        """Converts a FolioDetailsModel database entity into a FolioDetailsEntity domain entity."""
        return FolioDetailsEntity(
            uu_id=db_entity.uu_id,
            hotel_id=db_entity.hotel_id,
            bill_id=db_entity.bill_id,
            booking_id=db_entity.booking_id,
            category=db_entity.category,
            owner_name=db_entity.owner_name,
            folio_number=db_entity.folio_number,
            folio_status=db_entity.folio_status,
            billed_entity_id=db_entity.billed_entity_id,
            account_number=db_entity.account_number,
            booking_reference_number=db_entity.booking_reference_number,
            is_credit_folio=db_entity.is_credit_folio,
            payment_split_details=[
                PaymentSplitDetails(**detail)
                for detail in db_entity.payment_split_details
            ]
            if db_entity.payment_split_details
            else [],
            fin_erp_posting_date=db_entity.fin_erp_posting_date,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
