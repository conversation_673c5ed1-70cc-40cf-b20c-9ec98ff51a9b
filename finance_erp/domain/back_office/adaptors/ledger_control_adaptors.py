from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    GLLedgerItem,
)
from finance_erp.domain.back_office.models import CLLedgerItemModel, GLLedgerItemModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class GLLedgerItemAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: GLLedgerItem, **kwargs) -> GLLedgerItemModel:
        # noinspection PyArgumentList
        return GLLedgerItemModel(
            id=domain_entity.id,
            posting_date=domain_entity.posting_date,
            hotel_id=domain_entity.hotel_id,
            erp_name=domain_entity.erp_name,
            particular=domain_entity.particular,
            txn_amount=domain_entity.txn_amount,
            txn_group=domain_entity.txn_group,
            gl_code=domain_entity.gl_code,
            txn_id=domain_entity.txn_id,
            debit=domain_entity.debit,
            credit=domain_entity.credit,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: GLLedgerItemModel, **kwargs) -> GLLedgerItem:
        return GLLedgerItem(
            id=db_entity.id,
            posting_date=db_entity.posting_date,
            hotel_id=db_entity.hotel_id,
            erp_name=db_entity.erp_name,
            particular=db_entity.particular,
            txn_amount=db_entity.txn_amount,
            txn_group=db_entity.txn_group,
            gl_code=db_entity.gl_code,
            txn_id=db_entity.txn_id,
            debit=db_entity.debit,
            credit=db_entity.credit,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )


class CLLedgerItemAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CLLedgerItem, **kwargs) -> CLLedgerItemModel:
        # noinspection PyArgumentList
        return CLLedgerItemModel(
            id=domain_entity.id,
            account_number=domain_entity.account_number,
            account_name=domain_entity.account_name,
            guest_name=domain_entity.guest_name,
            posting_date=domain_entity.posting_date,
            hotel_id=domain_entity.hotel_id,
            erp_name=domain_entity.erp_name,
            payment_type=domain_entity.payment_type,
            txt_date=domain_entity.txt_date,
            checkin_date=domain_entity.checkin_date,
            checkout_date=domain_entity.checkout_date,
            txn_amount=domain_entity.txn_amount,
            folio_number=domain_entity.folio_number,
            booking_id=domain_entity.booking_id,
            room_number=domain_entity.room_number,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: CLLedgerItem, **kwargs) -> CLLedgerItem:
        return CLLedgerItem(
            id=db_entity.id,
            account_number=db_entity.account_number,
            account_name=db_entity.account_name,
            guest_name=db_entity.guest_name,
            posting_date=db_entity.posting_date,
            hotel_id=db_entity.hotel_id,
            erp_name=db_entity.erp_name,
            payment_type=db_entity.payment_type,
            txt_date=db_entity.txt_date,
            checkin_date=db_entity.checkin_date,
            checkout_date=db_entity.checkout_date,
            txn_amount=db_entity.txn_amount,
            folio_number=db_entity.folio_number,
            booking_id=db_entity.booking_id,
            room_number=db_entity.room_number,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
