from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.models import TransactionMaster
from finance_erp.domain.back_office.value_objects import TransactionMetaData
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class TransactionMasterAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: TransactionMasterEntity, **kwargs):
        # noinspection PyArgumentList
        return TransactionMaster(
            transaction_id=domain_entity.transaction_id,
            hotel_id=domain_entity.hotel_id,
            erp_name=domain_entity.erp_name,
            gl_code=domain_entity.gl_code,
            is_active=domain_entity.is_active,
            merge_gl_entries=domain_entity.merge_gl_entries,
            particulars=domain_entity.particulars,
            identifier_name=domain_entity.identifier_name,
            display_name=domain_entity.display_name,
            revenue_center=domain_entity.revenue_center,
            identifier=domain_entity.identifier,
            transaction_type=domain_entity.transaction_type,
            transaction_metadata=domain_entity.transaction_metadata.to_json()
            if domain_entity.transaction_metadata
            else None,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: TransactionMaster, **kwargs):
        return TransactionMasterEntity(
            transaction_id=db_entity.transaction_id,
            hotel_id=db_entity.hotel_id,
            erp_name=db_entity.erp_name,
            gl_code=db_entity.gl_code,
            is_active=db_entity.is_active,
            merge_gl_entries=db_entity.merge_gl_entries,
            particulars=db_entity.particulars,
            identifier_name=db_entity.identifier_name,
            display_name=db_entity.display_name,
            revenue_center=db_entity.revenue_center,
            identifier=db_entity.identifier,
            transaction_type=db_entity.transaction_type,
            transaction_metadata=TransactionMetaData(
                tax_code=db_entity.transaction_metadata.get("tax_code"),
                tax_type=db_entity.transaction_metadata.get("tax_type"),
                tax_value=db_entity.transaction_metadata.get("tax_value"),
                sku_category=db_entity.transaction_metadata.get("sku_category"),
                payment_mode_sub_type=db_entity.transaction_metadata.get(
                    "payment_mode_sub_type"
                ),
            )
            if db_entity.transaction_metadata
            else None,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            deleted=db_entity.deleted,
            is_new=False,
        )
