from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.models import PaymentModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class PaymentAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PaymentEntity, **kwargs) -> PaymentModel:
        """Converts a PaymentEntity domain entity into a PaymentModel database entity."""
        return PaymentModel(
            uu_id=domain_entity.uu_id,
            bill_id=domain_entity.bill_id,
            payment_id=domain_entity.payment_id,
            payment_split_id=domain_entity.payment_split_id,
            payment_type=domain_entity.payment_type,
            amount=domain_entity.amount,
            posting_date=domain_entity.posting_date,
            date_of_payment=domain_entity.date_of_payment,
            payment_mode=domain_entity.payment_mode,
            crs_payment_mode=domain_entity.crs_payment_mode,
            crs_payment_mode_sub_type=domain_entity.crs_payment_mode_sub_type,
            payment_mode_sub_type=domain_entity.payment_mode_sub_type,
            payment_ref_id=domain_entity.payment_ref_id,
            payment_channel=domain_entity.payment_channel,
            hotel_id=domain_entity.hotel_id,
            revenue_center=domain_entity.revenue_center,
            booking_id=domain_entity.booking_id,
            category=domain_entity.category,
            owner_name=domain_entity.owner_name,
            folio_number=domain_entity.folio_number,
            debtor_code=domain_entity.debtor_code,
            checkin_date=domain_entity.checkin_date,
            checkout_date=domain_entity.checkout_date,
            room_number=domain_entity.room_number,
            billed_entity_id=domain_entity.billed_entity_id,
            account_number=domain_entity.account_number,
            booking_reference_number=domain_entity.booking_reference_number,
            fin_erp_posting_date=domain_entity.fin_erp_posting_date,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: PaymentModel, **kwargs) -> PaymentEntity:
        """Converts a PaymentModel database entity into a PaymentEntity domain entity."""
        return PaymentEntity(
            uu_id=db_entity.uu_id,
            bill_id=db_entity.bill_id,
            payment_id=db_entity.payment_id,
            payment_split_id=db_entity.payment_split_id,
            payment_type=db_entity.payment_type,
            amount=db_entity.amount,
            posting_date=db_entity.posting_date,
            date_of_payment=db_entity.date_of_payment,
            payment_mode=db_entity.payment_mode,
            crs_payment_mode=db_entity.crs_payment_mode,
            crs_payment_mode_sub_type=db_entity.crs_payment_mode_sub_type,
            payment_mode_sub_type=db_entity.payment_mode_sub_type,
            payment_ref_id=db_entity.payment_ref_id,
            payment_channel=db_entity.payment_channel,
            hotel_id=db_entity.hotel_id,
            revenue_center=db_entity.revenue_center,
            booking_id=db_entity.booking_id,
            category=db_entity.category,
            owner_name=db_entity.owner_name,
            folio_number=db_entity.folio_number,
            debtor_code=db_entity.debtor_code,
            checkin_date=db_entity.checkin_date,
            checkout_date=db_entity.checkout_date,
            room_number=db_entity.room_number,
            billed_entity_id=db_entity.billed_entity_id,
            account_number=db_entity.account_number,
            fin_erp_posting_date=db_entity.fin_erp_posting_date,
            booking_reference_number=db_entity.booking_reference_number,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
