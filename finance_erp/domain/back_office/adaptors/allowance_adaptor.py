from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.models import AllowanceModel
from finance_erp.domain.back_office.value_objects import TaxDetails
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class AllowanceAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: AllowanceEntity, **kwargs) -> AllowanceModel:
        """Converts an AllowanceEntity domain entity into an AllowanceModel database entity."""
        return AllowanceModel(
            uu_id=domain_entity.uu_id,
            bill_id=domain_entity.bill_id,
            allowance_id=domain_entity.allowance_id,
            charge_id=domain_entity.charge_id,
            charge_split_id=domain_entity.charge_split_id,
            posting_date=domain_entity.posting_date,
            tax_amount=domain_entity.tax_amount,
            posttax_amount=domain_entity.posttax_amount,
            pretax_amount=domain_entity.pretax_amount,
            tax_details=[
                tax_detail.to_json() for tax_detail in domain_entity.tax_details
            ]
            if domain_entity.tax_details
            else None,
            charge_type=domain_entity.charge_type,
            bill_to_type=domain_entity.bill_to_type,
            item_id=domain_entity.item_id,
            sku_category_id=domain_entity.sku_category_id,
            hotel_id=domain_entity.hotel_id,
            revenue_center=domain_entity.revenue_center,
            booking_id=domain_entity.booking_id,
            category=domain_entity.category,
            owner_name=domain_entity.owner_name,
            folio_number=domain_entity.folio_number,
            billed_entity_id=domain_entity.billed_entity_id,
            account_number=domain_entity.account_number,
            booking_reference_number=domain_entity.booking_reference_number,
            fin_erp_posting_date=domain_entity.fin_erp_posting_date,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: AllowanceModel, **kwargs) -> AllowanceEntity:
        """Converts an AllowanceModel database entity into an AllowanceEntity domain entity."""
        return AllowanceEntity(
            uu_id=db_entity.uu_id,
            allowance_id=db_entity.allowance_id,
            bill_id=db_entity.bill_id,
            charge_id=db_entity.charge_id,
            charge_split_id=db_entity.charge_split_id,
            posting_date=db_entity.posting_date,
            tax_amount=db_entity.tax_amount,
            posttax_amount=db_entity.posttax_amount,
            pretax_amount=db_entity.pretax_amount,
            tax_details=[
                TaxDetails(**tax_detail) for tax_detail in db_entity.tax_details
            ]
            if db_entity.tax_details
            else None,
            charge_type=db_entity.charge_type,
            bill_to_type=db_entity.bill_to_type,
            item_id=db_entity.item_id,
            sku_category_id=db_entity.sku_category_id,
            hotel_id=db_entity.hotel_id,
            revenue_center=db_entity.revenue_center,
            booking_id=db_entity.booking_id,
            category=db_entity.category,
            owner_name=db_entity.owner_name,
            folio_number=db_entity.folio_number,
            billed_entity_id=db_entity.billed_entity_id,
            account_number=db_entity.account_number,
            booking_reference_number=db_entity.booking_reference_number,
            fin_erp_posting_date=db_entity.fin_erp_posting_date,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
