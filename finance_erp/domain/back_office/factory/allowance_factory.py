from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.value_objects import TaxDetails


class AllowanceFactory:
    @classmethod
    def create_data_from_dict(cls, data_dict: dict, posting_date) -> AllowanceEntity:
        """Create an AllowanceEntity from a dictionary of allowance data."""
        tax_details = (
            [
                TaxDetails(**tax_detail)
                for tax_detail in data_dict.get("tax_details", [])
            ]
            if data_dict.get("tax_details")
            else None
        )
        data_dict.update(
            {
                "uu_id": fin_erp_random_id_generator("ALW", max_length=20),
                "bill_id": data_dict.get("bill_id"),
                "allowance_id": data_dict.get("allowance_id"),
                "charge_id": data_dict.get("charge_id"),
                "charge_split_id": data_dict.get("charge_split_id"),
                "posting_date": data_dict.get("posting_date"),
                "tax_amount": data_dict.get("tax_amount"),
                "posttax_amount": data_dict.get("posttax_amount"),
                "pretax_amount": data_dict.get("pretax_amount"),
                "charge_type": data_dict.get("charge_type"),
                "bill_to_type": data_dict.get("bill_to_type"),
                "item_id": data_dict.get("item_id"),
                "sku_category_id": data_dict.get("sku_category_id"),
                "hotel_id": data_dict.get("hotel_id"),
                "booking_id": data_dict.get("booking_id"),
                "category": data_dict.get("category"),
                "owner_name": data_dict.get("owner_name"),
                "folio_number": data_dict.get("folio_number"),
                "billed_entity_id": data_dict.get("billed_entity_id"),
                "account_number": data_dict.get("account_number"),
                "booking_reference_number": data_dict.get("booking_reference_number"),
                "revenue_center": data_dict.get("revenue_center"),
                "fin_erp_posting_date": data_dict.get("fin_erp_posting_date")
                or posting_date,
                "tax_details_of_charge": tax_details,
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
                "deleted": False,
            }
        )
        return AllowanceEntity.model_validate(data_dict)
