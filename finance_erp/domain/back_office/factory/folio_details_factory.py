from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.value_objects import PaymentSplitDetails


class FolioDetailsFactory:
    @classmethod
    def create_data_from_dict(cls, data_dict: dict, posting_date) -> FolioDetailsEntity:
        """Create a FolioDetailsEntity from a dictionary of folio details data."""
        payment_split_details = (
            [
                PaymentSplitDetails(**payment_split)
                for payment_split in data_dict.get("payment_split_details", [])
            ]
            if data_dict.get("payment_split_details")
            else None
        )
        data_dict.update(
            {
                "uu_id": fin_erp_random_id_generator("FOL", max_length=20),
                "hotel_id": data_dict.get("hotel_id"),
                "bill_id": data_dict.get("bill_id"),
                "booking_id": data_dict.get("booking_id"),
                "category": data_dict.get("category"),
                "owner_name": data_dict.get("owner_name"),
                "folio_number": data_dict.get("folio_number"),
                "folio_status": data_dict.get("folio_status"),
                "billed_entity_id": data_dict.get("billed_entity_id"),
                "account_number": data_dict.get("account_number"),
                "booking_reference_number": data_dict.get("booking_reference_number"),
                "is_credit_folio": data_dict.get("is_credit_folio"),
                "fin_erp_posting_date": data_dict.get("fin_erp_posting_date")
                or posting_date,
                "payment_split_details": payment_split_details,
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
                "deleted": False,
                "room_number": data_dict.get("room_number"),
            }
        )
        return FolioDetailsEntity.model_validate(data_dict)
