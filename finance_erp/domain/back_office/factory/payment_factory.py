from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity


class PaymentFactory:
    @classmethod
    def create_data_from_dict(cls, data_dict: dict, posting_date) -> PaymentEntity:
        """Create a PaymentEntity from a dictionary of payment data."""
        data_dict.update(
            {
                "uu_id": fin_erp_random_id_generator("PAY", max_length=20),
                "bill_id": data_dict.get("bill_id"),
                "payment_id": data_dict.get("payment_id"),
                "payment_split_id": data_dict.get("payment_split_id"),
                "payment_type": data_dict.get("payment_type"),
                "amount": data_dict.get("amount"),
                "posting_date": data_dict.get("posting_date") or posting_date,
                "date_of_payment": data_dict.get("date_of_payment") or posting_date,
                "payment_mode": data_dict.get("payment_mode"),
                "crs_payment_mode": data_dict.get("crs_payment_mode"),
                "payment_mode_sub_type": data_dict.get("payment_mode_sub_type"),
                "payment_ref_id": data_dict.get("payment_ref_id"),
                "payment_channel": data_dict.get("payment_channel"),
                "hotel_id": data_dict.get("hotel_id"),
                "booking_id": data_dict.get("booking_id"),
                "category": data_dict.get("category"),
                "owner_name": data_dict.get("owner_name"),
                "folio_number": data_dict.get("folio_number"),
                "checkin_date": data_dict.get("checkin_date"),
                "checkout_date": data_dict.get("checkout_date"),
                "booking_reference_number": data_dict.get("booking_reference_number"),
                "revenue_center": data_dict.get("revenue_center"),
                "fin_erp_posting_date": posting_date,
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
                "deleted": False,
                "billed_entity_id": data_dict.get("billed_entity_id"),
                "account_number": data_dict.get("account_number"),
                "room_number": data_dict.get("room_number"),
                "debtor_code": data_dict.get("debtor_code"),
                "crs_payment_mode_sub_type": data_dict.get("crs_payment_mode_sub_type"),
            }
        )
        return PaymentEntity.model_validate(data_dict)
