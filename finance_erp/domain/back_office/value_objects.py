from typing import Optional

from pydantic import BaseModel


class TransactionMetaData(BaseModel):
    tax_code: Optional[str] = None
    tax_type: Optional[str] = None
    tax_value: Optional[float] = None
    sku_category: Optional[str] = None
    payment_mode_sub_type: Optional[str] = None

    def to_json(self) -> dict:
        return self.model_dump()


class TaxDetails(BaseModel):
    percentage: Optional[float] = None
    tax_type: Optional[str] = None
    amount: Optional[float] = None

    def to_json(self) -> dict:
        return self.model_dump()


class PaymentSplitDetails(BaseModel):
    payment_split_id: Optional[int] = None
    amount: Optional[float] = None
    payment_id: Optional[int] = None

    def to_json(self) -> dict:
        return self.model_dump()
