from collections import defaultdict
from typing import List, Optional

from pydantic import BaseModel

from finance_erp.domain.back_office.constants import AccountMasterTypes, GLRevenueTypes
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)


class TransactionMasterLookupTable(BaseModel):
    hotel_id: str
    txn_master_codes: List[TransactionMasterEntity]

    def __init__(self, **data):
        super().__init__(**data)
        self._transaction_lookup_table = defaultdict(lambda: defaultdict(list))
        for transaction_code in self.txn_master_codes:
            self._transaction_lookup_table[transaction_code.transaction_type][
                transaction_code.identifier
            ].append(transaction_code)

    def get_txn_master(
        self,
        txn_type: str,
        identifier: str,
        revenue_center: str,
        tax_percentage: Optional[float] = None,
        tax_type: Optional[str] = None,
    ) -> TransactionMasterEntity:
        txn_masters: List[TransactionMasterEntity] = self._transaction_lookup_table[
            txn_type
        ][identifier]
        for txn_master in txn_masters:
            if txn_master.revenue_center == revenue_center:
                if txn_type in [
                    GLRevenueTypes.TAX_ON_CHARGE,
                    GLRevenueTypes.TAX_ON_ALLOWANCE,
                ]:
                    if (
                        txn_master.transaction_metadata
                        and txn_master.transaction_metadata.tax_value == tax_percentage
                        and txn_master.transaction_metadata.tax_code == tax_type
                    ):
                        return txn_master
                else:
                    return txn_master
        raise KeyError(
            "Transaction Master not found %s %s %s"
            % (txn_type, identifier, revenue_center)
        )

    def get_customer_advance_master(self) -> TransactionMasterEntity:
        try:
            return self._transaction_lookup_table[GLRevenueTypes.PAYMENT][
                AccountMasterTypes.CUSTOMER_ADVANCE
            ][0]
        except (KeyError, IndexError):
            raise KeyError("Transaction Master not found for customer advance")

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
