from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel, field_validator
from ths_common.constants.billing_constants import PaymentTypes
from treebo_commons.utils.dateutils import ymd_str_to_date


class PaymentEntity(BaseModel):
    uu_id: Optional[str] = None
    bill_id: Optional[str] = None
    payment_id: Optional[int] = None
    payment_split_id: Optional[int] = None
    payment_type: Optional[str] = None
    amount: Optional[float] = None
    posting_date: Optional[date] = None
    date_of_payment: Optional[datetime] = None
    payment_mode: Optional[str] = None
    crs_payment_mode: Optional[str] = None
    payment_mode_sub_type: Optional[str] = None
    payment_ref_id: Optional[str] = None
    payment_channel: Optional[str] = None
    hotel_id: Optional[str] = None
    booking_id: Optional[str] = None
    category: Optional[str] = None
    owner_name: Optional[str] = None
    folio_number: Optional[int] = None
    checkin_date: Optional[datetime] = None
    checkout_date: Optional[datetime] = None
    booking_reference_number: Optional[str] = None
    revenue_center: Optional[str] = None
    fin_erp_posting_date: Optional[date] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    deleted: Optional[bool] = None
    billed_entity_id: Optional[int] = None
    account_number: Optional[int] = None
    room_number: Optional[str] = None
    debtor_code: Optional[str] = None
    crs_payment_mode_sub_type: Optional[str] = None

    @classmethod
    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @classmethod
    @field_validator("checkin_date", mode="before")
    def parse_checkin_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v).date()
        return v

    @classmethod
    @field_validator("checkout_date", mode="before")
    def parse_checkout_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v).date()
        return v

    @classmethod
    @field_validator("fin_erp_posting_date", mode="before")
    def parse_fin_erp_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @classmethod
    @field_validator("date_of_payment", mode="before")
    def parse_date_of_payment(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    def to_json(self) -> dict:
        return self.model_dump()

    @property
    def is_advance(self) -> bool:
        return self.fin_erp_posting_date < self.checkin_date.date()

    @property
    def is_refund(self) -> bool:
        return self.payment_type == PaymentTypes.REFUND.value

    @property
    def guest_name(self) -> Optional[str]:
        return self.owner_name

    @property
    def payment_mode_identifier(self) -> str:
        return (
            f"{self.payment_mode}#{self.payment_mode_sub_type}"
            if self.payment_mode_sub_type
            else self.payment_mode
        )

    def mark_as_deleted(self) -> None:
        self.deleted = True

    class Config:
        from_attributes = True
        validate_by_name = True
