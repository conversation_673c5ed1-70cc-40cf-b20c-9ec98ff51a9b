from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.domain.back_office.constants import FolioStatus
from finance_erp.domain.back_office.value_objects import PaymentSplitDetails


class FolioDetailsEntity(BaseModel):
    uu_id: str
    hotel_id: Optional[str] = None
    bill_id: Optional[str] = None
    booking_id: Optional[str] = None
    category: Optional[str] = None
    owner_name: Optional[str] = None
    folio_number: Optional[int] = None
    folio_status: Optional[str] = None
    billed_entity_id: Optional[int] = None
    account_number: Optional[int] = None
    booking_reference_number: Optional[str] = None
    is_credit_folio: Optional[bool] = None
    fin_erp_posting_date: date
    payment_split_details: Optional[List[PaymentSplitDetails]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    deleted: bool = False
    room_number: Optional[int] = None

    @classmethod
    @field_validator("fin_erp_posting_date", mode="before")
    def parse_empty_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @property
    def is_invoice_locked(self) -> bool:
        return self.folio_status == FolioStatus.INVOICE_LOCKED

    @property
    def is_invoice_reissued(self) -> bool:
        return self.folio_status == FolioStatus.INVOICE_REISSUED

    @property
    def guest_name(self) -> str:
        return self.owner_name

    def mark_as_deleted(self):
        self.deleted = True

    def to_json(self) -> dict:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True
        frozen = False
