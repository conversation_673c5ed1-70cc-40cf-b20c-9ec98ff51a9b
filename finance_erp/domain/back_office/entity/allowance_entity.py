from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.domain.back_office.value_objects import TaxDetails


class AllowanceEntity(BaseModel):
    uu_id: str
    bill_id: Optional[str] = None
    allowance_id: Optional[int] = None
    charge_id: Optional[int] = None
    charge_split_id: Optional[int] = None
    posting_date: Optional[date] = None
    tax_amount: Optional[float] = None
    posttax_amount: Optional[float] = None
    pretax_amount: Optional[float] = None
    charge_type: Optional[str] = None
    bill_to_type: Optional[str] = None
    item_id: Optional[str] = None
    sku_category_id: Optional[str] = None
    hotel_id: Optional[str] = None
    booking_id: Optional[str] = None
    category: Optional[str] = None
    owner_name: Optional[str] = None
    folio_number: Optional[int] = None
    billed_entity_id: Optional[int] = None
    account_number: Optional[int] = None
    booking_reference_number: Optional[str] = None
    revenue_center: Optional[str] = None
    fin_erp_posting_date: Optional[date] = None
    tax_details: Optional[List[TaxDetails]] = None
    tax_details_of_charge: Optional[List[TaxDetails]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    deleted: Optional[bool] = False

    @classmethod
    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @classmethod
    @field_validator("fin_erp_posting_date", mode="before")
    def parse_fin_erp_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @property
    def sku_id(self) -> str:
        return self.item_id

    def to_json(self) -> dict:
        return self.model_dump()

    def mark_as_deleted(self):
        self.deleted = True

    def get_tax_item_with_restored_tax_percentage(
        self, tax_item: TaxDetails
    ) -> TaxDetails:
        if self.tax_details_of_charge:
            for charge_tax_item in self.tax_details_of_charge:
                if charge_tax_item.tax_type == tax_item.tax_type:
                    return TaxDetails(
                        percentage=charge_tax_item.percentage,
                        tax_type=tax_item.tax_type,
                        amount=tax_item.amount,
                    )
        return tax_item

    class Config:
        from_attributes = True
        validate_by_name = True
        frozen = False
