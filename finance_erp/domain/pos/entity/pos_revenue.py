from datetime import date, datetime, time
from typing import List, Optional

from finance_erp.domain.base import (
    ERPEntityMixin,
    ERPEntityStatus,
    ERPEntityStatusLiteral,
)

UPDATABLE_FIELDS = ["verified"]


class POSRevenueItem(ERPEntityMixin):
    uu_id: str
    interface_id: str
    interface_name: str
    hotel_id: str
    bill_id: str
    reservation_id: Optional[str] = None
    amount: float
    tax: float
    sku_category: str
    hsn_code: str
    payment_method: str
    payment_mode_sub_type: Optional[str]
    pos_bill_date: date
    pos_bill_time: time
    guest_name: Optional[str] = None
    tax_details: Optional[List[dict]] = None
    revenue_center: Optional[str] = None
    serving_time: Optional[str] = None
    workstation_id: Optional[str] = None
    waiter_id: Optional[str] = None
    sku_id: Optional[str] = None
    verified: bool = True
    status: ERPEntityStatusLiteral = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None

    def get_unique_identifier(self) -> str:
        return self.uu_id

    @property
    def is_allowance(self) -> bool:
        return self.amount < 0

    @property
    def payment_mode(self) -> str:
        return self.payment_method

    @property
    def payment_mode_identifier(self) -> str:
        return (
            f"{self.payment_mode}#{self.payment_mode_sub_type}"
            if self.payment_mode_sub_type
            else self.payment_mode
        )

    @property
    def pretax_amount(self) -> float:
        return self.amount - self.tax

    class Config:
        validate_by_name = True
        from_attributes = True
