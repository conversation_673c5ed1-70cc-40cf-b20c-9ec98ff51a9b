from typing import Dict

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    Date,
    DateTime,
    Index,
    String,
    Time,
)
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class POSRevenueItemModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "pos_revenue_item"

    uu_id = Column(String, primary_key=True)
    interface_id = Column(String)
    interface_name = Column(String)
    hotel_id = Column(String, nullable=False)
    bill_id = Column(String, nullable=False)
    reservation_id = Column(String)
    guest_name = Column(String, nullable=True)
    amount = Column(DECIMAL, nullable=False)
    tax = Column(DECIMAL)
    tax_details = Column(JSON)
    sku_category = Column(String, nullable=False)
    hsn_code = Column(String, nullable=False)
    payment_method = Column(String, nullable=False)
    payment_mode_sub_type = Column(String, nullable=True)
    pos_bill_date = Column(Date, nullable=False)
    pos_bill_time = Column(Time, nullable=False)
    revenue_center = Column(String, nullable=True)
    serving_time = Column(String, nullable=True)
    sku_id = Column(String, nullable=True)
    workstation_id = Column(String, nullable=True)
    waiter_id = Column(String, nullable=True)

    verified = Column(Boolean, default=True)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    __table_args__ = (
        Index("ix_pos_revenue_item_bill_id", "bill_id"),
        Index("ix_pos_revenue_item_hotel_id", "hotel_id"),
        Index("ix_pos_revenue_item_reservation_id", "reservation_id"),
        Index("ix_pos_revenue_item_pos_bill_date", "pos_bill_date"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "uu_id": self.uu_id,
            "interface_id": self.interface_id,
            "interface_name": self.interface_name,
            "hotel_id": self.hotel_id,
            "bill_id": self.bill_id,
            "reservation_id": self.reservation_id,
            "guest_name": self.guest_name,
            "amount": self.amount,
            "tax": self.tax,
            "tax_details": self.tax_details,
            "sku_category": self.sku_category,
            "hsn_code": self.hsn_code,
            "payment_method": self.payment_method,
            "payment_mode_sub_type": self.payment_mode_sub_type,
            "pos_bill_date": self.pos_bill_date,
            "pos_bill_time": self.pos_bill_time,
            "revenue_center": self.revenue_center,
            "serving_time": self.serving_time,
            "sku_id": self.sku_id,
            "waiter_id": self.waiter_id,
            "workstation_id": self.workstation_id,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
