from typing import Any, Dict, List

from finance_erp.domain.pos.adaptors.pos_revenue_adaptor import POSRevenueAdaptor
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from finance_erp.domain.pos.models import POSRevenueItemModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class POSRevenueRepository(BaseRepository):
    pos_revenue_adaptor = POSRevenueAdaptor()

    def to_entity(self, model):
        return self.pos_revenue_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.pos_revenue_adaptor.to_db_entity(entity)

    def insert_many(self, pos_revenue_list: List[POSRevenueItem]):
        self._bulk_insert_mappings(
            POSRevenueItemModel,
            [
                self.from_entity(pos_revenue).mapping_dict()
                for pos_revenue in pos_revenue_list
            ],
        )
        self.flush_session()

    def filter_by_conditions(
        self, filters: Dict[str, Any], limit: int = None, offset: int = None
    ) -> List[POSRevenueItem]:
        query = self.query(POSRevenueItemModel)

        if filters.get("hotel_id"):
            query = query.filter(POSRevenueItemModel.hotel_id == filters["hotel_id"])

        if filters.get("reservation_id"):
            query = query.filter(
                POSRevenueItemModel.reservation_id == filters["reservation_id"]
            )

        if filters.get("bill_id"):
            query = query.filter(POSRevenueItemModel.bill_id == filters["bill_id"])

        if filters.get("date"):
            query = query.filter(POSRevenueItemModel.pos_bill_date == filters["date"])

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]

    def get(self, uu_id):
        db_model = super().get_one(POSRevenueItemModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        limit: int = None,
        offset: int = None,
    ) -> List[POSRevenueItem]:
        query = self.query(POSRevenueItemModel).filter(
            POSRevenueItemModel.deleted.is_(False)
        )

        if hotel_id:
            query = query.filter(POSRevenueItemModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(POSRevenueItemModel.pos_bill_date == posting_date)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]
