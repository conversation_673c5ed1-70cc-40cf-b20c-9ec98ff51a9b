from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem


class POSRevenueItemFactory:
    @classmethod
    def create_pos_revenue_item_from_data_dict(cls, data_dict: dict):
        data_dict.update(
            {
                "uu_id": fin_erp_random_id_generator("POS", max_length=20),
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
            }
        )
        return POSRevenueItem.model_validate(data_dict)
