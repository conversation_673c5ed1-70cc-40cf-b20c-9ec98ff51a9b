from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from finance_erp.domain.pos.models import POSRevenueItemModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class POSRevenueAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: POSRevenueItem, **kwargs):
        # noinspection PyArgumentList
        return POSRevenueItemModel(
            uu_id=domain_entity.uu_id,
            interface_id=domain_entity.interface_id,
            interface_name=domain_entity.interface_name,
            hotel_id=domain_entity.hotel_id,
            bill_id=domain_entity.bill_id,
            reservation_id=domain_entity.reservation_id,
            guest_name=domain_entity.guest_name,
            amount=domain_entity.amount,
            tax=domain_entity.tax,
            tax_details=domain_entity.tax_details,
            sku_category=domain_entity.sku_category,
            hsn_code=domain_entity.hsn_code,
            payment_method=domain_entity.payment_method,
            payment_mode_sub_type=domain_entity.payment_mode_sub_type,
            pos_bill_date=domain_entity.pos_bill_date,
            pos_bill_time=domain_entity.pos_bill_time,
            revenue_center=domain_entity.revenue_center,
            serving_time=domain_entity.serving_time,
            sku_id=domain_entity.sku_id,
            workstation_id=domain_entity.workstation_id,
            waiter_id=domain_entity.waiter_id,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: POSRevenueItemModel, **kwargs):
        return POSRevenueItem(
            uu_id=db_entity.uu_id,
            interface_id=db_entity.interface_id,
            interface_name=db_entity.interface_name,
            hotel_id=db_entity.hotel_id,
            bill_id=db_entity.bill_id,
            reservation_id=db_entity.reservation_id,
            guest_name=db_entity.guest_name,
            amount=float(db_entity.amount) if db_entity.amount is not None else 0.0,
            tax=float(db_entity.tax) if db_entity.tax is not None else 0.0,
            tax_details=db_entity.tax_details,
            sku_category=db_entity.sku_category,
            hsn_code=db_entity.hsn_code,
            payment_method=db_entity.payment_method,
            payment_mode_sub_type=db_entity.payment_mode_sub_type,
            pos_bill_date=db_entity.pos_bill_date,
            pos_bill_time=db_entity.pos_bill_time,
            revenue_center=db_entity.revenue_center,
            serving_time=db_entity.serving_time,
            sku_id=db_entity.sku_id,
            workstation_id=db_entity.workstation_id,
            waiter_id=db_entity.waiter_id,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
        )
