from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity


class SalesSummaryEntityFactory:
    @staticmethod
    def create_sales_summary_from_sales_invoice_entity(
        sales_invoice: SalesInvoiceEntity,
    ):
        data_dict = {
            "unique_ref_id": fin_erp_random_id_generator("IV", max_length=20),
            "entry_type": sales_invoice.entry_type,
            "hsn_code": sales_invoice.hsn_code,
            "order_date": sales_invoice.order_date,
            "posting_date": sales_invoice.posting_date,
            "uvid_date": sales_invoice.uvid_date,
            "unit_price": float(sales_invoice.unit_price)
            if sales_invoice.unit_price
            else 0.0,
            "tax_percentage": float(sales_invoice.tax_percentage)
            if sales_invoice.tax_percentage
            else 0.0,
            "tax_type": sales_invoice.tax_type,
            "cgst": float(sales_invoice.cgst or 0.0),
            "sgst": float(sales_invoice.sgst or 0.0),
            "igst": float(sales_invoice.igst or 0.0),
            "is_new": True,
        }
        return SalesSummaryEntity.model_validate(data_dict)
