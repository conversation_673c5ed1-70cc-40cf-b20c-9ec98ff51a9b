from finance_erp.common.constants import PurchaseInvoiceReportCategory
from finance_erp.common.utils.checksum_creator import get_data_hash_20
from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity


class PurchaseFactory:
    @staticmethod
    def create_from_data_dict(purchase_data: dict):
        purchase_data.update(
            {
                "sub_source": get_data_hash_20(purchase_data.get("sub_source")),
                "vendor_number": "NOT_AVAILABLE",
            }
        )
        return PurchaseInvoiceEntity.model_validate(purchase_data)
