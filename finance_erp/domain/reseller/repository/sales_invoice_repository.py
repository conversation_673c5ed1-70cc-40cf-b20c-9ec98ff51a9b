from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.reseller.adaptors.sale_invoice_adaptor import SaleInvoiceAdaptor
from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.models import SaleInvoiceModel, SalesInvoiceStatus
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class SalesInvoiceRepository(BaseRepository):
    adapter = SaleInvoiceAdaptor()

    def to_entity(self, model):
        return self.adapter.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adapter.to_db_entity(entity)

    def bulk_update_records(self, invoices: List[SalesInvoiceEntity]):
        self.bulk_update_entities(
            SaleInvoiceModel,
            invoices,
        )
        self.flush_session()

    def insert_many(self, invoices):
        self._bulk_insert_mappings(
            SaleInvoiceModel,
            [self.from_entity(invoice).mapping_dict() for invoice in invoices],
        )
        self.flush_session()

    def get_reports(self, invoice_numbers=None, unique_ref_id=None):
        if not (unique_ref_id or invoice_numbers):
            raise Exception("invalid parameter")
        q = self.query(SaleInvoiceModel).filter(SaleInvoiceModel.deleted == False)
        if invoice_numbers:
            q.filter(SaleInvoiceModel.invoice_number.in_(tuple(invoice_numbers)))
        else:
            q.filter(SaleInvoiceModel.unique_ref_id.in_(tuple(unique_ref_id)))
        db_models = q.all()
        return [self.to_entity(db_model) for db_model in db_models]

    def get(self, uu_id):
        db_model = super().get_one(SaleInvoiceModel, unique_ref_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            SaleInvoiceModel,
            SaleInvoiceModel.unique_ref_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_records(self, uu_ids=None, invoice_numbers=None, status_to_exclude=None):
        q = self.query(SaleInvoiceModel).filter(SaleInvoiceModel.deleted == False)
        if invoice_numbers:
            q = q.filter(SaleInvoiceModel.invoice_number.in_(tuple(invoice_numbers)))
        if uu_ids:
            q = q.filter(SaleInvoiceModel.unique_ref_id.in_(tuple(uu_ids)))
        if status_to_exclude:
            q = q.filter(SaleInvoiceModel.status != status_to_exclude)
        db_models = q.all()
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date(self, source_created_on):
        db_models = self.filter(
            SaleInvoiceModel, SaleInvoiceModel.source_created_on == source_created_on
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(SaleInvoiceModel)
            .filter(
                SaleInvoiceModel.modified_at >= from_date_gte,
                SaleInvoiceModel.modified_at <= to_date_lte,
                SaleInvoiceModel.deleted == False,
            )
            .order_by(SaleInvoiceModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(SaleInvoiceModel, [record])
        self.flush_session()

    def get_eligible_records_to_aggregates(self, yield_count=2000):
        db_models = (
            self.session()
            .query(SaleInvoiceModel)
            .filter(
                SaleInvoiceModel.verified == True,
                SaleInvoiceModel.status == SalesInvoiceStatus.INGESTED,
                SaleInvoiceModel.deleted == False,
            )
            .yield_per(yield_count)
            .with_for_update(nowait=True)
            .all()
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_by_aggregation_ids(self, aggregation_ids, for_update=False):
        db_models = self.filter(
            SaleInvoiceModel,
            SaleInvoiceModel.aggregation_id.in_(aggregation_ids),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]
