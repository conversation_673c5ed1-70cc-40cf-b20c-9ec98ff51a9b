from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.reseller.adaptors.purchase_invoice_adaptor import (
    PurchaseInvoiceAdaptor,
)
from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity
from finance_erp.domain.reseller.models import PurchaseInvoiceModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PurchaseInvoiceRepository(BaseRepository):
    adapter = PurchaseInvoiceAdaptor()

    def to_entity(self, model):
        return self.adapter.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adapter.to_db_entity(entity)

    def bulk_update_records(self, invoices: List[PurchaseInvoiceEntity]):
        self.bulk_update_entities(
            PurchaseInvoiceModel,
            invoices,
        )
        self.flush_session()

    def insert_many(self, invoices, **kwargs):
        self._bulk_insert_mappings(
            PurchaseInvoiceModel,
            [self.from_entity(invoice).mapping_dict() for invoice in invoices],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(PurchaseInvoiceModel, unique_ref_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            PurchaseInvoiceModel,
            PurchaseInvoiceModel.unique_ref_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def load_all(
        self,
        uu_ids=None,
        invoice_numbers=None,
        source_created_on=None,
        only_pushed_records=False,
        load_for_update=False,
    ):
        q = self.query(PurchaseInvoiceModel).filter(
            PurchaseInvoiceModel.deleted == False
        )
        if uu_ids:
            q = q.filter(PurchaseInvoiceModel.unique_ref_id.in_(tuple(uu_ids)))
        if invoice_numbers:
            q = q.filter(
                PurchaseInvoiceModel.invoice_number.in_(tuple(invoice_numbers))
            )
        if only_pushed_records:
            q = q.filter(PurchaseInvoiceModel.status == ERPEntityStatus.PUSHED)
        if source_created_on:
            q = q.filter(PurchaseInvoiceModel.source_created_on == source_created_on)
        if load_for_update:
            q = q.with_for_update(nowait=True)
        db_models = q.all()
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(PurchaseInvoiceModel)
            .filter(
                PurchaseInvoiceModel.modified_at >= from_date_gte,
                PurchaseInvoiceModel.modified_at <= to_date_lte,
                PurchaseInvoiceModel.deleted == False,
            )
            .order_by(PurchaseInvoiceModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(PurchaseInvoiceModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(PurchaseInvoiceModel)
            .filter(
                PurchaseInvoiceModel.verified == True,
                PurchaseInvoiceModel.status.in_(
                    ERPEntityStatus.allowed_status_for_data_push()
                ),
                PurchaseInvoiceModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(PurchaseInvoiceModel)
            .filter(
                PurchaseInvoiceModel.unique_ref_id.in_(tuple(uu_ids)),
                PurchaseInvoiceModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(PurchaseInvoiceModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
