from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.reseller.adaptors.sales_summary_adaptor import (
    SaleInvoiceSummaryAdaptor,
)
from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity
from finance_erp.domain.reseller.models import SalesInvoiceStatus, SalesSummaryModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class SalesInvoiceSummaryRepository(BaseRepository):
    adapter = SaleInvoiceSummaryAdaptor()

    def to_entity(self, model):
        return self.adapter.to_domain_entity(model)

    def from_entity(self, entity):
        return self.adapter.to_db_entity(entity)

    def bulk_update_records(self, invoices: List[SalesSummaryEntity]):
        self.bulk_update_entities(
            SalesSummaryModel,
            invoices,
        )
        self.flush_session()

    def insert_many(self, invoices):
        self._bulk_insert_mappings(
            SalesSummaryModel,
            [self.from_entity(invoice).mapping_dict() for invoice in invoices],
        )
        self.flush_session()

    def get_reports(self, unique_ref_id):
        q = self.query(SalesSummaryModel).filter(SalesSummaryModel.deleted == False)
        db_models = q.filter(
            SalesSummaryModel.unique_ref_id.in_(tuple(unique_ref_id))
        ).all()
        return [self.to_entity(db_model) for db_model in db_models]

    def get(self, uu_id):
        db_model = super().get_one(SalesSummaryModel, unique_ref_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            SalesSummaryModel,
            SalesSummaryModel.unique_ref_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(SalesSummaryModel)
            .filter(
                SalesSummaryModel.modified_at >= from_date_gte,
                SalesSummaryModel.modified_at <= to_date_lte,
                SalesSummaryModel.deleted == False,
            )
            .order_by(SalesSummaryModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(SalesSummaryModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self, posting_date=None):
        filters = [
            SalesSummaryModel.status.in_(
                SalesInvoiceStatus.allowed_status_for_data_push()
            ),
            SalesSummaryModel.deleted == False,
        ]
        if posting_date:
            filters.append(
                SalesSummaryModel.posting_date <= posting_date,
            )
        db_models = (
            self.session()
            .query(SalesSummaryModel)
            .filter(*filters)
            .with_for_update(nowait=True)
            .all()
        )
        return [self.to_entity(db_model) for db_model in db_models]
