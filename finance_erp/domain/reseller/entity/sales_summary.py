# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin, ERPEntityStatus

SALES_UPDATABLE_ATTRIBUTES = ["verified"]


@change_tracker
class SalesSummaryEntity(ERPEntityMixin, ChangeTrackerMixin):
    entry_type: str
    order_date: Optional[date] = None
    posting_date: Optional[date] = None
    unit_price: float
    tax_percentage: int
    uvid_date: Optional[date] = None
    unique_ref_id: str
    hsn_code: str
    cgst: float
    sgst: float
    igst: float
    tax_type: Optional[str] = None
    status: str = ERPEntityStatus.INGESTED
    verified: bool = True
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    checksum: Optional[str] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    is_new: bool = True
    is_dirty: bool = True

    def to_json(self):
        return self.model_dump()

    def get_unique_identifier(self):
        return self.unique_ref_id

    def update(self, data: dict):
        self.entry_type = data.get("entry_type")
        self.order_date = data.get("order_date")
        self.posting_date = data.get("posting_date")
        self.unit_price = data.get("unit_price")
        self.tax_percentage = data.get("tax_percentage")
        self.cgst = data.get("cgst")
        self.igst = data.get("igst")
        self.sgst = data.get("sgst")
        self.tax_type = data.get("tax_type")
        self.hsn_code = data.get("hsn_code")
        self.status = ERPEntityStatus.INGESTED

    def aggregate(self, sales_invoice_entity):
        self.unit_price = round(
            float(self.unit_price)
            + (
                float(sales_invoice_entity.unit_price)
                if sales_invoice_entity.unit_price
                else 0.0
            ),
            2,
        )
        self.cgst = round(float(self.cgst) + float(sales_invoice_entity.cgst or 0.0), 2)
        self.sgst = round(float(self.sgst) + float(sales_invoice_entity.sgst or 0.0), 2)
        self.igst = round(float(self.igst) + float(sales_invoice_entity.igst or 0.0), 2)
        self.is_dirty = True

    class Config:
        from_attributes = True
        validate_by_name = True
