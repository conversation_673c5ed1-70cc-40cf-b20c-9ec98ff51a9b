from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity
from finance_erp.domain.reseller.models import PurchaseInvoiceModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class PurchaseInvoiceAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PurchaseInvoiceEntity, **kwargs):
        # noinspection PyArgumentList
        return PurchaseInvoiceModel(
            vendor_number=domain_entity.vendor_number,
            remark=domain_entity.remark,
            hsn_code=domain_entity.hsn_code,
            original_invoice_number=domain_entity.original_invoice_number,
            source_created_on=domain_entity.source_created_on,
            structure=domain_entity.structure,
            nature_of_supply=domain_entity.nature_of_supply,
            gst_vendor_type=domain_entity.gst_vendor_type,
            entry_type=domain_entity.entry_type,
            due_date=domain_entity.due_date,
            order_date=domain_entity.order_date,
            posting_date=domain_entity.posting_date,
            uvid_date=domain_entity.uvid_date,
            check_in=domain_entity.check_in,
            check_out=domain_entity.check_out,
            reference_number=domain_entity.reference_number,
            state_code=domain_entity.state_code,
            unit_price=domain_entity.unit_price,
            tax_percentage=domain_entity.tax_percentage,
            hotel_name=domain_entity.hotel_name,
            stay_days=domain_entity.stay_days,
            room_type=domain_entity.room_type,
            occupancy=domain_entity.occupancy,
            guest_name=domain_entity.guest_name,
            invoice_number=domain_entity.invoice_number,
            total_invoice_amount=domain_entity.total_invoice_amount,
            hotel_code=domain_entity.hotel_code,
            unique_ref_id=domain_entity.unique_ref_id,
            source=domain_entity.source,
            sub_source=domain_entity.sub_source,
            customer_invoice_number=domain_entity.customer_invoice_number,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
            purchase_type=domain_entity.purchase_type,
            report_category=domain_entity.report_category,
        )

    def to_domain_entity(self, db_entity: PurchaseInvoiceModel, **kwargs):
        return PurchaseInvoiceEntity(
            vendor_number=db_entity.vendor_number,
            remark=db_entity.remark,
            hsn_code=db_entity.hsn_code,
            original_invoice_number=db_entity.original_invoice_number,
            source_created_on=db_entity.source_created_on,
            structure=db_entity.structure,
            nature_of_supply=db_entity.nature_of_supply,
            gst_vendor_type=db_entity.gst_vendor_type,
            entry_type=db_entity.entry_type,
            due_date=db_entity.due_date,
            order_date=db_entity.order_date,
            posting_date=db_entity.posting_date,
            check_out=db_entity.check_out,
            check_in=db_entity.check_in,
            uvid_date=db_entity.uvid_date,
            reference_number=db_entity.reference_number,
            state_code=db_entity.state_code,
            unit_price=db_entity.unit_price,
            tax_percentage=db_entity.tax_percentage,
            hotel_name=db_entity.hotel_name,
            stay_days=db_entity.stay_days,
            room_type=db_entity.room_type,
            occupancy=db_entity.occupancy,
            guest_name=db_entity.guest_name,
            invoice_number=db_entity.invoice_number,
            total_invoice_amount=db_entity.total_invoice_amount,
            hotel_code=db_entity.hotel_code,
            unique_ref_id=db_entity.unique_ref_id,
            source=db_entity.source,
            sub_source=db_entity.sub_source,
            customer_invoice_number=db_entity.customer_invoice_number,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            purchase_type=db_entity.purchase_type,
            report_category=db_entity.report_category,
        )
