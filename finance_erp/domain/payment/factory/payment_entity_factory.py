from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.value_objects import BookingOwner


class PaymentFactory:
    @classmethod
    def create_payment_from_data_dict(cls, data_dict):
        booking_owner_data = data_dict.get("booking_owner")
        booking_owner = (
            BookingOwner.model_validate(booking_owner_data)
            if booking_owner_data
            else None
        )
        data_dict["booking_owner"] = booking_owner
        return PGPaymentEntity.model_validate(data_dict)
