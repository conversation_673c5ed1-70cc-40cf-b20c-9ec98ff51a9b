from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.payment.entity.payment_summary_entity import (
    PGPaymentSummaryEntity,
)
from finance_erp.domain.payment.value_objects import BookingOwner


class PaymentSummaryFactory:
    @classmethod
    def create_payment_summary_from_payment_entity(
        cls, data_dict: dict, is_ar_entry: bool = False
    ):
        booking_owner_data = data_dict.get("booking_owner")
        booking_owner = (
            BookingOwner.model_validate(booking_owner_data)
            if booking_owner_data
            else None
        )
        data_dict.update(
            {
                "pg_transaction_id": ""
                if is_ar_entry
                else data_dict.get("pg_transaction_id"),
                "payor_entity": "" if is_ar_entry else data_dict.get("payor_entity"),
                "athena_code": "" if is_ar_entry else data_dict.get("athena_code"),
                "payor_name": "" if is_ar_entry else data_dict.get("payor_name"),
                "booking_owner": booking_owner,
                "uu_id": fin_erp_random_id_generator("PAY", max_length=20),
            }
        )
        return PGPaymentSummaryEntity.model_validate(data_dict)
