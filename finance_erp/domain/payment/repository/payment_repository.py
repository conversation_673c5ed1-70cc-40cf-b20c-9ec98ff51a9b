from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.payment.adaptors.payment_adaptor import PaymentAdaptor
from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.models import PGPaymentModel, PGPaymentStatus
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PaymentRepository(BaseRepository):
    payment_adaptor = PaymentAdaptor()

    def to_entity(self, model):
        return self.payment_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.payment_adaptor.to_db_entity(entity)

    def insert_many(self, payment_list: List[PGPaymentEntity]):
        self._bulk_insert_mappings(
            PGPaymentModel,
            [self.from_entity(payment).mapping_dict() for payment in payment_list],
        )
        self.flush_session()

    def bulk_update_records(self, payment_list: List[PGPaymentEntity]):
        self.bulk_update_entities(
            PGPaymentModel,
            payment_list,
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(PGPaymentModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            PGPaymentModel,
            PGPaymentModel.uu_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_by_pg_ids(self, pg_ids, for_update=False):
        db_models = self.filter(
            PGPaymentModel,
            PGPaymentModel.pg_transaction_id.in_(pg_ids),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(PGPaymentModel)
            .filter(
                PGPaymentModel.modified_at >= from_date_gte,
                PGPaymentModel.modified_at <= to_date_lte,
                PGPaymentModel.deleted == False,
            )
            .order_by(PGPaymentModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: PGPaymentEntity):
        self.bulk_update_entities(PGPaymentModel, [record])
        self.flush_session()

    def get_pushed_records_by_uuid(self, uu_ids, status_to_exclude=None):
        q = self.query(PGPaymentModel).filter(
            PGPaymentModel.uu_id.in_(tuple(uu_ids)),
        )
        if status_to_exclude:
            q = q.filter(PGPaymentModel.status != status_to_exclude)
        q = q.order_by(PGPaymentModel.modified_at.desc())
        db_models = q.all()
        return [self.to_entity(db_model) for db_model in db_models]

    def get_eligible_records_to_aggregates(self):
        db_models = (
            self.session()
            .query(PGPaymentModel)
            .filter(
                PGPaymentModel.verified == True,
                PGPaymentModel.status == PGPaymentStatus.INGESTED,
                PGPaymentModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_by_aggregation_ids(self, aggregation_ids, for_update=False):
        db_models = self.filter(
            PGPaymentModel,
            PGPaymentModel.aggregation_id.in_(aggregation_ids),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]
