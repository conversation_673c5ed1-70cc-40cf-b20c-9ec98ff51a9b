from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.payment.adaptors.payment_summary_adaptor import (
    PaymentSummaryAdaptor,
)
from finance_erp.domain.payment.entity.payment_summary_entity import (
    PGPaymentSummaryEntity,
)
from finance_erp.domain.payment.models import PGPaymentStatus, PGPaymentSummaryModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PaymentSummaryRepository(BaseRepository):
    payment_summary_daptor = PaymentSummaryAdaptor()

    def to_entity(self, model):
        return self.payment_summary_daptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.payment_summary_daptor.to_db_entity(entity)

    def insert_many(self, payment_list: List[PGPaymentSummaryEntity]):
        self._bulk_insert_mappings(
            PGPaymentSummaryModel,
            [self.from_entity(payment).mapping_dict() for payment in payment_list],
        )
        self.flush_session()

    def bulk_update_records(self, payment_list: List[PGPaymentSummaryEntity]):
        self.bulk_update_entities(
            PGPaymentSummaryModel,
            payment_list,
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(PGPaymentSummaryModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            PGPaymentSummaryModel,
            PGPaymentSummaryModel.uu_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_by_pg_ids(self, pg_ids, for_update=False):
        db_models = self.filter(
            PGPaymentSummaryModel,
            PGPaymentSummaryModel.pg_transaction_id.in_(pg_ids),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(PGPaymentSummaryModel)
            .filter(
                PGPaymentSummaryModel.modified_at >= from_date_gte,
                PGPaymentSummaryModel.modified_at <= to_date_lte,
                PGPaymentSummaryModel.deleted == False,
            )
            .order_by(PGPaymentSummaryModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: PGPaymentSummaryEntity):
        self.bulk_update_entities(PGPaymentSummaryModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self, date):
        query = (
            self.session()
            .query(PGPaymentSummaryModel)
            .filter(
                PGPaymentSummaryModel.status.in_(
                    PGPaymentStatus.allowed_status_for_data_push()
                ),
                PGPaymentSummaryModel.deleted == False,
                PGPaymentSummaryModel.posting_date <= date,
            )
        )
        db_models = query.with_for_update(nowait=True).all()

        return [self.to_entity(db_model) for db_model in db_models]
