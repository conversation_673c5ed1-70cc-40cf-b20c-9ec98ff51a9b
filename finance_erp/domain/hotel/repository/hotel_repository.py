from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.hotel.adaptors.hotel_adaptor import HotelAdaptor
from finance_erp.domain.hotel.entity.hotel import HotelEntity
from finance_erp.domain.hotel.models import HotelModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class HotelReportRepository(BaseRepository):
    hotel_adaptor = HotelAdaptor()

    def to_entity(self, model):
        return self.hotel_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.hotel_adaptor.to_db_entity(entity)

    def bulk_update_records(self, hotel_list: List[HotelEntity]):
        self.bulk_update_entities(HotelModel, hotel_list)
        self.flush_session()

    def insert_many(self, hotel_list: List[HotelEntity]):
        self._bulk_insert_mappings(
            HotelModel, [self.from_entity(hotel).mapping_dict() for hotel in hotel_list]
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(HotelModel, hotel_code=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            HotelModel, HotelModel.hotel_code.in_(tuple(uu_ids)), for_update=for_update
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(HotelModel)
            .filter(
                HotelModel.modified_at >= from_date_gte,
                HotelModel.modified_at <= to_date_lte,
                HotelModel.deleted == False,
            )
            .order_by(HotelModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(HotelModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(HotelModel)
            .filter(
                HotelModel.verified == True,
                HotelModel.status.in_(ERPEntityStatus.allowed_status_for_data_push()),
                HotelModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]
