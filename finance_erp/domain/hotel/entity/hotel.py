from datetime import datetime
from typing import Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.common.constants import NavisionReports
from finance_erp.common.utils.checksum_creator import generate_check_sum
from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin, ERPEntityStatus

UPDATABLE_FIELDS = ("verified",)


@change_tracker
class HotelEntity(ERPEntityMixin, ChangeTrackerMixin):
    vendor_name: Optional[str] = None
    hotel_code: str
    search_name: str
    address: Optional[str] = None
    city: Optional[str] = None
    country_code: Optional[str] = None
    pan: Optional[str] = None
    state_code: Optional[str] = None
    gstin: Optional[str] = None
    gst_vendor_type: str
    msme: Optional[str] = None
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    cost_center_id: Optional[str] = None

    def to_json(self):
        """Convert the object to a JSON."""
        return self.model_dump()

    def get_unique_identifier(self):
        return self.hotel_code

    @property
    def data_checksum(self):
        return generate_check_sum(NavisionReports.HOTEL_REPORT, data_class=self)

    def update(self, data: dict):
        self.vendor_name = data.get("vendor_name")
        self.search_name = data.get("search_name")
        self.address = data.get("address")
        self.city = data.get("city")
        self.country_code = data.get("country_code")
        self.pan = data.get("pan")
        self.state_code = data.get("state_code")
        self.gstin = data.get("gstin")
        self.gst_vendor_type = data.get("gst_vendor_type")
        self.msme = data.get("msme")
        self.cost_center_id = data.get("cost_center_id")

        self.verified = True
        if self.pushed:
            self.erp_remarks = f"{self.erp_remarks} on {self.last_push_attempt_at}, data change after that"
            self.last_push_attempt_at = None
        self.status = ERPEntityStatus.PUSHED

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
