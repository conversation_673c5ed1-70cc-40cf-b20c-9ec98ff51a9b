from finance_erp.domain.hotel.entity.hotel import HotelEntity
from finance_erp.domain.hotel.models import HotelModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class HotelAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelEntity, **kwargs):
        # noinspection PyArgumentList
        return HotelModel(
            vendor_name=domain_entity.vendor_name,
            hotel_code=domain_entity.hotel_code,
            search_name=domain_entity.search_name,
            address=domain_entity.address,
            city=domain_entity.city,
            country_code=domain_entity.country_code,
            pan=domain_entity.pan,
            state_code=domain_entity.state_code,
            gstin=domain_entity.gstin,
            gst_vendor_type=domain_entity.gst_vendor_type,
            msme=domain_entity.msme,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
            cost_center_id=domain_entity.cost_center_id,
        )

    def to_domain_entity(self, db_entity: HotelModel, **kwargs):
        return HotelEntity(
            vendor_name=db_entity.vendor_name,
            hotel_code=db_entity.hotel_code,
            search_name=db_entity.search_name,
            address=db_entity.address,
            city=db_entity.city,
            country_code=db_entity.country_code,
            pan=db_entity.pan,
            state_code=db_entity.state_code,
            gstin=db_entity.gstin,
            gst_vendor_type=db_entity.gst_vendor_type,
            msme=db_entity.msme,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            cost_center_id=db_entity.cost_center_id,
        )
