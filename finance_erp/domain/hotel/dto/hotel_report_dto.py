from typing import Any, Dict, Optional

from pydantic import BaseModel, model_validator

from finance_erp.application.common.utils import (
    get_state_code,
    get_state_code_from_gstin,
    sanitize_string,
)
from finance_erp.common.constants import GSTType


class HotelReportDto(BaseModel):
    hotel_data: Dict[str, Any] = None
    vendor_name: str
    search_name: str
    address: Optional[str] = None
    city: str
    country_code: str
    pan: Optional[str] = None
    state_code: str
    gstin: Optional[str] = None
    hotel_code: str
    msme: Optional[str] = None
    tan_number: Optional[str] = None
    gst_vendor_type: str
    cost_center_id: Optional[str] = None

    @model_validator(mode="before")
    def populate_fields(cls, values):
        hotel_data = values
        gstin = hotel_data.get("property_details", {}).get("gstin", "")
        values["vendor_name"] = hotel_data.get("name", {}).get("legal_name", "")
        values["search_name"] = hotel_data.get("name", {}).get("new_name", "")
        legal_address = hotel_data.get("location", {}).get("legal_address")
        values["address"] = (
            sanitize_string(legal_address, length=100) if legal_address else None
        )
        city_name = hotel_data.get("location", {}).get("city", {}).get("name", "")
        values["city"] = sanitize_string(city_name, only_alpha=True)
        values["country_code"] = (
            hotel_data.get("location", {}).get("country", {}).get("iso_code", "")
        )
        values["pan"] = hotel_data.get("property_details", {}).get("pan", "")
        values["gstin"] = gstin
        if gstin:
            values["state_code"] = get_state_code_from_gstin(gstin[0:2])
        else:
            state_name = hotel_data.get("location", {}).get("state", {}).get("name", "")
            values["state_code"] = get_state_code(state_name)
        values["hotel_code"] = hotel_data.get("id", "")
        values["msme"] = None  # Placeholder for future integration
        values["tan_number"] = None  # Placeholder for future integration
        values["gst_vendor_type"] = (
            GSTType.REGISTERED.value if gstin else GSTType.UNREGISTERED.value
        )
        values["cost_center_id"] = hotel_data.get("cost_center_id", "")
        return values

    def to_dict(self):
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
