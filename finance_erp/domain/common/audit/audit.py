from finance_erp.audit_extension import audit_manager
from finance_erp.domain.auth.models import (
    PrivilegesModel,
    RolesModel,
    RolesPrivilegesModel,
    UserGroupModel,
    UserModel,
)
from finance_erp.domain.back_office.models import TransactionMaster
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.domain.hotel.models import HotelModel


def attach_auditing():
    """
    Attach auditing listeners to all relevant models.
    """
    models = [
        UserModel,
        UserGroupModel,
        RolesPrivilegesModel,
        RolesModel,
        PrivilegesModel,
        HotelModel,
        CorporateModel,
        TransactionMaster,
    ]
    audit_manager.attach_auditing_listeners(models)
