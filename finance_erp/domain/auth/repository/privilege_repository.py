from finance_erp.common.decorators import session_manager
from finance_erp.domain.auth.entity.privilege import Privilege
from finance_erp.domain.auth.models import PrivilegesModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PrivilegesRepository(BaseRepository):
    def to_entity(self, db_model: PrivilegesModel):
        return Privilege(id=db_model.id, name=db_model.name)

    def from_entity(self, domain_entity: Privilege):
        return PrivilegesModel(name=domain_entity.name)

    def find(self, name):
        role = (
            self.query(PrivilegesModel).filter(
                PrivilegesModel.name == name,
                PrivilegesModel.deleted == False,
            )
        ).first()
        return self.to_entity(role) if role else None

    @session_manager(commit=True)
    def save_privilege(self, privilege: Privilege):
        if not self.find(privilege.name):
            self.update(self.from_entity(privilege))
        return self.find(privilege.name)
