from finance_erp.common.decorators import session_manager
from finance_erp.domain.auth.entity.role import Role
from finance_erp.domain.auth.models import (
    RolesModel,
    RolesPrivilegesModel,
    UserGroupModel,
)
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class RolesRepository(BaseRepository):
    def to_entity(self, db_model: RolesModel):
        return Role(id=db_model.id, name=db_model.name)

    def from_entity(self, domain_entity: Role):
        return RolesModel(name=domain_entity.name)

    def find(self, name):
        role = (
            self.query(RolesModel).filter(
                RolesModel.name == name,
                RolesModel.deleted == False,
            )
        ).first()
        return self.to_entity(role) if role else None

    @session_manager(commit=True)
    def save_role(self, role: Role):
        if not self.find(role.name):
            self.update(self.from_entity(role))
        return self.find(role.name)

    def is_privilege_mapped_to_role(self, role_id, privilege_id):
        return (
            self.query(RolesPrivilegesModel)
            .filter_by(role_id=role_id, privilege_id=privilege_id)
            .first()
            is not None
        )

    def map_privilege_to_role(self, role_id, privilege_id):
        mapping = RolesPrivilegesModel(role_id=role_id, privilege_id=privilege_id)
        self.update(mapping)
        return mapping
