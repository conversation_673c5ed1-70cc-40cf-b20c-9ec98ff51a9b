from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, UniqueConstraint
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class UserModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "users"

    email = Column(String, primary_key=True)
    authenticated = Column(Boolean, default=True, nullable=False)


class UserGroupModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "user_groups"

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String, unique=True)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=True)
    role = relationship("RolesModel", back_populates="user_groups")


class RolesPrivilegesModel(Base):
    __tablename__ = "roles_privileges"

    id = Column(Integer, primary_key=True, autoincrement=True)
    role_id = Column(Integer, <PERSON><PERSON><PERSON>("roles.id"), nullable=False)
    privilege_id = Column(Integer, ForeignKey("privileges.id"), nullable=False)

    role = relationship("RolesModel", back_populates="roles_privileges")
    privilege = relationship("PrivilegesModel", back_populates="roles_privileges")

    __table_args__ = (
        UniqueConstraint("role_id", "privilege_id", name="role_privilege_unique"),
    )


class RolesModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)

    user_groups = relationship("UserGroupModel", back_populates="role")

    roles_privileges = relationship("RolesPrivilegesModel", back_populates="role")

    privileges = relationship(
        "PrivilegesModel", secondary="roles_privileges", back_populates="roles"
    )


class PrivilegesModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "privileges"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String)

    roles_privileges = relationship("RolesPrivilegesModel", back_populates="privilege")

    roles = relationship(
        "RolesModel", secondary="roles_privileges", back_populates="privileges"
    )
