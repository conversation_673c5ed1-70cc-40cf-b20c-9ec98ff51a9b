from finance_erp.common.exception import PolicyAuthException
from finance_erp.domain.policy.errors import PolicyError
from finance_erp.domain.policy.facts.facts import Facts
from finance_erp.domain.policy.rule.base import BaseRule
from finance_erp.infrastructure.external_clients.role_privilege.privilege_constants import (
    PrivilegeCode,
)


class LedgersDataRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.BACK_OFFICE_LEDGERS_DATA_ACCESS not in privileges:
            raise PolicyAuthException(
                error=PolicyError.ACCESSING_BACKOFFICE_DATA_NOT_ALLOWED
            )
        return True
