from finance_erp.common.constants import TACommissionEntryType
from finance_erp.domain.ta_commission.entity.ta_commission import TACommissionEntity
from finance_erp.domain.ta_commission.models import TaCommissionModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class TaCommissionAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: TACommissionEntity, **kwargs):
        # noinspection PyArgumentList
        return TaCommissionModel(
            posting_date=domain_entity.posting_date,
            commission_amount=domain_entity.commission_amount,
            reference_number=domain_entity.reference_number,
            uu_id=domain_entity.uu_id,
            entry_type=domain_entity.entry_type,
            hotel_code=domain_entity.hotel_code,
            check_in=domain_entity.check_in,
            check_out=domain_entity.check_out,
            pretax_room_rent=domain_entity.pretax_room_rent,
            paid_at_ota=domain_entity.paid_at_ota,
            ta_name=domain_entity.ta_name,
            guest_name=domain_entity.guest_name,
            ta_sh_profile_code=domain_entity.ta_sh_profile_code,
            commission_percent=domain_entity.commission_percent,
            booking_created_date=domain_entity.booking_created_date,
            mop=domain_entity.mop,
            tds_percentage=domain_entity.tds_percentage,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: TaCommissionModel, **kwargs):
        return TACommissionEntity(
            posting_date=db_entity.posting_date,
            commission_amount=db_entity.commission_amount,
            reference_number=db_entity.reference_number,
            uu_id=db_entity.uu_id,
            entry_type=TACommissionEntryType(db_entity.entry_type).value,
            hotel_code=db_entity.hotel_code,
            check_in=db_entity.check_in,
            check_out=db_entity.check_out,
            pretax_room_rent=db_entity.pretax_room_rent,
            paid_at_ota=db_entity.paid_at_ota,
            ta_name=db_entity.ta_name,
            ta_sh_profile_code=db_entity.ta_sh_profile_code,
            guest_name=db_entity.guest_name,
            commission_percent=db_entity.commission_percent,
            booking_created_date=db_entity.booking_created_date,
            mop=db_entity.mop,
            tds_percentage=db_entity.tds_percentage,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
        )
