from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>olean, Column, Date, DateTime, Index, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class TaCommissionModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "ta_commission"

    posting_date = Column(Date)
    commission_amount = Column(DECIMAL)
    reference_number = Column(String)
    entry_type = Column(String)
    uu_id = Column(String, primary_key=True)
    hotel_code = Column(String)
    check_in = Column(Date)
    check_out = Column(Date)
    pretax_room_rent = Column(DECIMAL)
    paid_at_ota = Column(DECIMAL)
    guest_name = Column(String)
    ta_name = Column(String)
    ta_sh_profile_code = Column(String)
    commission_percent = Column(DECIMAL)
    booking_created_date = Column(Date)
    tds_percentage = Column(String)
    mop = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    __table_args__ = (
        Index("ix_ta_commission_reference_number", "reference_number"),
        Index("ix_ta_commission_reference_number_status", "reference_number", "status"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "posting_date": self.posting_date,
            "commission_amount": self.commission_amount,
            "reference_number": self.reference_number,
            "uu_id": self.uu_id,
            "entry_type": self.entry_type,
            "hotel_code": self.hotel_code,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "pretax_room_rent": self.pretax_room_rent,
            "paid_at_ota": self.paid_at_ota,
            "ta_name": self.ta_name,
            "ta_sh_profile_code": self.ta_sh_profile_code,
            "guest_name": self.guest_name,
            "commission_percent": self.commission_percent,
            "booking_created_date": self.booking_created_date,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
            "mop": self.mop,
            "tds_percentage": self.tds_percentage,
        }
