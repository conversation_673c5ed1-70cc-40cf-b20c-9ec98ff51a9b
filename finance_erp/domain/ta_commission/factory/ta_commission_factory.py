import copy

from ths_common.utils.id_generator_utils import random_id_generator

from finance_erp.common.constants import TACommissionEntryType
from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.ta_commission.entity.ta_commission import TACommissionEntity


class TACommissionEntityFactory:
    @staticmethod
    def create_ta_commission_data_from_data_dict(data_dict: dict):
        data_dict.update(
            {
                "uu_id": random_id_generator("TAC"),
                "entry_type": TACommissionEntryType.CREDIT.value,
            }
        )
        return TACommissionEntity.model_validate(data_dict)

    @staticmethod
    def create_debit_entry_from_domain_entity(
        entity: TACommissionEntity, pretax_room_rent, commission_amount, posting_date
    ):
        new_entity: TACommissionEntity = copy.deepcopy(entity)
        new_entity.entry_type = TACommissionEntryType.DEBIT.value
        new_entity.pretax_room_rent = pretax_room_rent
        new_entity.commission_amount = commission_amount
        new_entity.posting_date = posting_date
        new_entity.status = ERPEntityStatus.INGESTED
        new_entity.last_push_attempt_at = None
        new_entity.erp_remarks = None
        new_entity.uu_id = random_id_generator(f"TAC/{TACommissionEntryType.DEBIT}")
        return new_entity
