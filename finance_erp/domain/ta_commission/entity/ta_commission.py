# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from pydantic import field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.domain.base import ERPEntityMixin, ERPEntityStatus

UPDATABLE_FIELDS = ["verified"]


class TACommissionEntity(ERPEntityMixin):
    guest_name: str
    ta_name: str
    ta_sh_profile_code: str
    commission_amount: float
    hotel_code: str
    reference_number: str
    uu_id: str
    check_in: Optional[date] = None
    check_out: Optional[date] = None
    pretax_room_rent: float
    paid_at_ota: float
    commission_percent: float
    booking_created_date: Optional[date] = None
    posting_date: Optional[date] = None
    entry_type: Optional[str] = None
    mop: Optional[str] = None
    tds_percentage: Optional[str] = None
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    cost_center_id: Optional[str] = None

    @field_validator("commission_amount", mode="before")
    def parse_commission_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("check_in", mode="before")
    def parse_check_in(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("check_out", mode="before")
    def parse_check_out(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("pretax_room_rent", mode="before")
    def parse_pretax_room_rent(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("paid_at_ota", mode="before")
    def parse_paid_at_ota(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("commission_percent", mode="before")
    def parse_commission_percent(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("booking_created_date", mode="before")
    def parse_booking_created_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    def to_json(self):
        """Convert the object to a JSON."""
        return self.model_dump()

    def get_unique_identifier(self):
        return self.uu_id

    def update(self, data: dict):
        self.guest_name = data.get("guest_name")
        self.ta_name = data.get("ta_name")
        self.ta_sh_profile_code = data.get("ta_sh_profile_code")
        self.commission_amount = data.get("commission_amount")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.pretax_room_rent = data.get("pretax_room_rent")
        self.paid_at_ota = data.get("paid_at_ota")
        self.mop = data.get("mop")
        self.tds_percentage = data.get("tds_percentage")
        self.commission_percent = data.get("commission_percent")
        self.booking_created_date = data.get("booking_created_date")
        self.verified = True
        self.status = ERPEntityStatus.INGESTED

    class Config:
        from_attributes = True
        validate_by_name = True
