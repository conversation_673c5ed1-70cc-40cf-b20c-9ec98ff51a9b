from datetime import time
from typing import List

from sqlalchemy.orm import aliased
from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.ta_commission.adaptors.ta_commission_adaptor import (
    TaCommissionAdaptor,
)
from finance_erp.domain.ta_commission.entity.ta_commission import TACommissionEntity
from finance_erp.domain.ta_commission.models import TaCommissionModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class TACommissionReportRepository(BaseRepository):
    ta_commission_adaptor = TaCommissionAdaptor()

    def to_entity(self, model):
        return self.ta_commission_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.ta_commission_adaptor.to_db_entity(entity)

    def insert_many(self, data_list: List[TACommissionEntity]):
        self._bulk_insert_mappings(
            TaCommissionModel,
            [self.from_entity(data).mapping_dict() for data in data_list],
        )
        self.flush_session()

    def bulk_update_records(self, data_list: List[TACommissionEntity]):
        self._bulk_update_mappings(
            TaCommissionModel,
            [self.from_entity(data).mapping_dict() for data in data_list],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = self.get_one(TaCommissionModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            TaCommissionModel,
            TaCommissionModel.uu_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_by_reference_numbers(self, reference_numbers, for_update=False):
        db_models = self.filter(
            TaCommissionModel,
            TaCommissionModel.reference_number.in_(tuple(reference_numbers)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date(self, source_created_on):
        db_models = self.filter(
            TaCommissionModel, TaCommissionModel.posting_date == source_created_on
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(TaCommissionModel)
            .filter(
                TaCommissionModel.modified_at >= from_date_gte,
                TaCommissionModel.modified_at <= to_date_lte,
            )
            .order_by(TaCommissionModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(TaCommissionModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(TaCommissionModel)
            .filter(
                TaCommissionModel.verified == True,
                TaCommissionModel.status.in_(
                    ERPEntityStatus.allowed_status_for_data_push()
                ),
                TaCommissionModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_booking_reference_numbers(self, reference_numbers):
        ta_cm_a = aliased(TaCommissionModel)
        ta_cm_b = aliased(TaCommissionModel)
        db_models = self.query(ta_cm_a).filter(
            self.query(ta_cm_b.reference_number)
            .filter(
                ta_cm_a.reference_number.in_(tuple(reference_numbers)),
                ta_cm_b.status == ERPEntityStatus.PUSHED,
                ta_cm_b.reference_number == ta_cm_a.reference_number,
            )
            .exists()
        )
        return [self.to_entity(db_model) for db_model in db_models]
