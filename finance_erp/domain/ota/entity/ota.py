# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin, ERPEntityStatus

UPDATABLE_FIELDS = ["verified"]


@change_tracker
class OTACommissionEntity(ERPEntityMixin, ChangeTrackerMixin):
    ota_name: str
    commission_amount: float
    hotel_code: str
    reference_number: str
    tds_percentage: str
    check_in: Optional[date] = None
    check_out: Optional[date] = None
    pretax_room_rent: float
    guest_name: str
    commission_percent: float
    mop: str
    booking_created_date: Optional[date] = None
    paid_at_ota: float
    posting_date: Optional[date] = None
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    cost_center_id: Optional[str] = None

    def to_json(self):
        """Convert the object to a JSON."""
        return self.model_dump()

    def get_unique_identifier(self):
        return self.reference_number

    def update(self, data: dict):
        self.ota_name = data.get("ota_name")
        self.commission_amount = data.get("commission_amount")
        self.hotel_code = data.get("hotel_code")
        self.tds_percentage = data.get("tds_percentage")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.pretax_room_rent = data.get("pretax_room_rent")
        self.guest_name = data.get("guest_name")
        self.commission_percent = data.get("commission_percent")
        self.mop = data.get("mop")
        self.booking_created_date = data.get("booking_created_date")
        self.paid_at_ota = data.get("paid_at_ota")
        self.posting_date = data.get("posting_date")
        self.verified = True
        self.status = ERPEntityStatus.INGESTED
        self.cost_center_id = data.get("cost_center_id")

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
