from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.ota.adaptors.ota_adaptor import OtaAdaptor
from finance_erp.domain.ota.entity.ota import OTACommissionEntity
from finance_erp.domain.ota.models import OtaModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class OTACommissionReportRepository(BaseRepository):
    ota_adaptor = OtaAdaptor()

    def to_entity(self, model):
        return self.ota_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.ota_adaptor.to_db_entity(entity)

    def insert_many(self, data_list: List[OTACommissionEntity]):
        self._bulk_insert_mappings(
            OtaModel, [self.from_entity(data).mapping_dict() for data in data_list]
        )
        self.flush_session()

    def bulk_update_records(self, data_list: List[OTACommissionEntity]):
        self._bulk_update_mappings(
            OtaModel, [self.from_entity(data).mapping_dict() for data in data_list]
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = self.get_one(OtaModel, reference_number=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            OtaModel,
            OtaModel.reference_number.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date(self, source_created_on):
        db_models = self.filter(OtaModel, OtaModel.posting_date == source_created_on)
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(OtaModel)
            .filter(
                OtaModel.modified_at >= from_date_gte,
                OtaModel.modified_at <= to_date_lte,
            )
            .order_by(OtaModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record):
        self.bulk_update_entities(OtaModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(OtaModel)
            .filter(
                OtaModel.verified == True,
                OtaModel.status.in_(ERPEntityStatus.allowed_status_for_data_push()),
                OtaModel.deleted == False,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(OtaModel)
            .filter(
                OtaModel.reference_number.in_(tuple(uu_ids)),
                OtaModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(OtaModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
