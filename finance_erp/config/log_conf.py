# coding=utf-8
"""
LOGGING CONF
"""
import os


def configure_logging(app):
    """
    Logging Setup
    :param app:
    :return:
    """
    import logging.config

    environment = os.environ.get("APP_ENV", "local")

    logging_conf = {
        "version": 1,
        "filters": {
            "request_id": {
                "()": "treebo_commons.request_tracing.log_filters.RequestContextFilter"
            }
        },
        "disable_existing_loggers": False,
        "formatters": {
            "verbose": {
                "format": "[%(asctime)s] %(levelname)s %(request_id)s - [%(name)s:%(lineno)s] %(message)s",
            },
            "logstash": {"()": "logstash_formatter.LogstashFormatterV1"},
        },
        "handlers": {
            "null": {
                "level": "DEBUG",
                "class": "logging.NullHandler",
                "filters": ["request_id"],
            },
            "console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "verbose"
                if environment in ("local", "testing")
                else "logstash",
                "filters": ["request_id"],
            },
        },
        "loggers": {
            "finance_erp": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
            "": {
                "handlers": ["console"],
                "level": "ERROR",
            },
            "treebo_commons.request_tracing": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False,
            },
            "request_handler": {
                "handlers": ["console"],
                "level": "INFO" if environment == "production" else "DEBUG",
                "propagate": False,
            },
            "core": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }
    logging.config.dictConfig(logging_conf)
