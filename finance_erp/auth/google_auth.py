import json
import logging

from flask import Blueprint
from flask import current_app as app
from flask import redirect, render_template, request, session, url_for
from flask_login import current_user, login_required, login_user, logout_user
from requests import HTTPError
from requests_oauthlib import OAuth2Session
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.request_tracing.context import get_current_tenant_id

from finance_erp.domain.auth.entity.user import User
from finance_erp.domain.auth.repository.user_group_repository import UserGroupRepository
from finance_erp.domain.auth.repository.user_repository import UserRepository
from object_registry import inject

flask_login_blue_print = Blueprint(
    "flask-login", __name__, url_prefix="/"
)  # pylint: disable=invalid-name
logger = logging.getLogger(__name__)


def get_google_auth(state=None, token=None):
    oauth2_cred = AwsSecretManager.get_secret(
        get_current_tenant_id(), "finance-erp-oauth2-cred"
    )
    auth = app.config.get("AUTH", {})
    if oauth2_cred:
        auth.update(oauth2_cred)
    if token:
        return OAuth2Session(auth["CLIENT_ID"], token=token)
    redirect_uri = f"https://{request.host}/erp/callback"
    if state:
        return OAuth2Session(auth["CLIENT_ID"], state=state, redirect_uri=redirect_uri)
    oauth = OAuth2Session(
        auth["CLIENT_ID"], redirect_uri=redirect_uri, scope=auth["SCOPE"]
    )
    return oauth


@flask_login_blue_print.route("/login")
def login():
    auth = app.config.get("AUTH", {})
    if current_user.is_authenticated:
        return redirect(url_for("admin.index"))
    google = get_google_auth()
    auth_url, state = google.authorization_url(auth["AUTH_URI"], access_type="offline")
    session["oauth_state"] = state
    return render_template("login.html", auth_url=auth_url)


# pylint: disable=too-many-return-statements
@flask_login_blue_print.route("/callback")
@inject(user_group_repository=UserGroupRepository, user_repository=UserRepository)
def callback(
    user_group_repository: UserGroupRepository, user_repository: UserRepository
):
    auth = app.config.get("AUTH", {})
    if current_user is not None and current_user.is_authenticated:
        return redirect(url_for("admin.index"))
    if "error" in request.args:
        if request.args.get("error") == "access_denied":
            return "You denied access"
        return "Error encountered while logging in"
    if "code" not in request.args and "state" not in request.args:
        return redirect("/erp/login")

    google = get_google_auth(state=session["oauth_state"])
    try:
        request_url = request.url.replace("http://", "https://")
        logger.info(f"Request URL: {request.url}")
        token = google.fetch_token(
            auth["TOKEN_URI"],
            client_secret=auth["CLIENT_SECRET"],
            authorization_response=request_url,
        )
    except HTTPError:
        return "HTTPError occurred."
    google = get_google_auth(token=token)
    resp = google.get(auth["USER_INFO"])
    if resp.status_code == 200:
        user_data = resp.json()
        email = user_data["email"]
        if not user_group_repository.find(email=email):
            return "You are not authorized to access"
        user = user_repository.save_user(User(email=email))
        user.tokens = json.dumps(token)
        login_user(user)
        if app.config.get("ENV") == "production":
            return redirect("/erp/admin")
        return redirect("/erp/admin/purchaseinvoicemodel")
    return "Could not fetch your information"


@flask_login_blue_print.route("/logout")
@login_required
def logout():
    logout_user()
    return redirect("/erp/login")
