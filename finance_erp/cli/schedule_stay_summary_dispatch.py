import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.decorators import session_manager
from finance_erp.common.globals import consumer_context
from object_registry import inject


@click.command("schedule_stay_summary_dispatch")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    "--stay_summary_id",
    help="Stay summary_id to dispatch",
    required=True,
)
@inject(
    job_scheduler=JobSchedulerService,
)
@with_appcontext
def schedule_stay_summary_dispatch(
    stay_summary_id,
    job_scheduler: JobSchedulerService,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    click.echo("Stay summary Id: %s" % stay_summary_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    schedule(job_scheduler, stay_summary_id)


@session_manager(commit=True)
def schedule(job_scheduler, stay_summary_id):
    job_scheduler.schedule_corporate_stay_summary_dispatch_job(stay_summary_id)
