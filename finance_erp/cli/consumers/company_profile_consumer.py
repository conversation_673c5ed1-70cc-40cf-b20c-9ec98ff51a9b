import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.application.consumers.company_profile_consumer import (
    CompanyProfileConsumer,
)
from finance_erp.application.corporate.command_handler.ingest_corporate_from_cp import (
    CPCorporateDataIngestionHandler,
)
from finance_erp.common.globals import consumer_context
from object_registry import inject


@click.command("company_profile_consumer")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@inject(
    cp_profile_ingestion_handler=CPCorporateDataIngestionHandler,
)
@with_appcontext
def company_profile_consumer(
    cp_profile_ingestion_handler,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    consumer = CompanyProfileConsumer(
        cp_profile_ingestion_handler,
        tenant_id,
    )
    consumer.start_consumer()
