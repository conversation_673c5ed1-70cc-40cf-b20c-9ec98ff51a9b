import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.application.consumers.crs_consumer import CRSConsumer
from finance_erp.application.corporate.communication.cn_dispatcher import (
    CorporateCreditNoteDispatcher,
)
from finance_erp.application.corporate.communication.invoice_dispatcher import (
    CorporateInvoiceDispatcher,
)
from finance_erp.application.crs.command_handlers.ingest_booking import (
    CrsBookingIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_credit_note import (
    CreditNoteIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_invoice import (
    InvoiceIngestionCommandHandler,
)
from finance_erp.common.globals import consumer_context
from object_registry import inject


@click.command("start_crs_consumer")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@inject(
    credit_note_ingestion_handler=CreditNoteIngestionCommandHandler,
    invoice_ingestion_handler=InvoiceIngestionCommandHandler,
    booking_ingestion_handler=CrsBookingIngestionCommandHandler,
    invoice_dispatcher=CorporateInvoiceDispatcher,
    credit_note_dispatcher=CorporateCreditNoteDispatcher,
)
@with_appcontext
def start_crs_consumer(
    credit_note_ingestion_handler,
    invoice_ingestion_handler,
    booking_ingestion_handler,
    invoice_dispatcher,
    credit_note_dispatcher,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    consumer = CRSConsumer(
        credit_note_ingestion_handler,
        invoice_ingestion_handler,
        booking_ingestion_handler,
        invoice_dispatcher,
        credit_note_dispatcher,
        tenant_id,
    )
    consumer.start_consumer()
