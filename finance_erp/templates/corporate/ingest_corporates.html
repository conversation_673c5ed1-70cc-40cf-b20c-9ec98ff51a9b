{% extends 'admin/master.html' %}
{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<body>
<h2>Sync Corporates</h2>
<div id="ManualIng">
    <div class="form-group">
        <label for="legalEntityIds">Legal Entity Ids</label>
        <input id="legalEntityIds" type="text" placeholder="Comma-Seperated Hotel-Ids (max 40)"  class="form-control"  aria-describedby="legalEntityIdsHelp">
        <small id="legalEntityIdsHelp" class="form-text text-muted">For Example : fcmte87f,nwgn8f24,.....</small>
    </div>
    <button id="AddBtn" class="btn btn-primary">Ingest</button>
</div>
<div class="loader"></div>
    <script>
            var $legalEntityIds = $('#legalEntityIds');
            $('#AddBtn').on('click',function(){
              var displayString = "Please fill ";
              var details = {
                "legal_entity_ids": $legalEntityIds.val().trim()
              }
              if(details.legal_entity_ids.length==0)
              {
                displayString=displayString.concat("Legal Entity Ids")
                alert(displayString);
                return;
              }
              if(details.legal_entity_ids.length>200)
              {
                displayString="Please provide string of length less than 200"
                alert(displayString);
                return;
              }
              $('.loader').css("visibility", "visible");
              fetch('/erp/api/v1/corporate-data/bulk-pull', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Application': 'FlaskAdminAPIRequest',
                        'X-Auth-Id': "{{ user_info.user_id }}",
                        'X-User-Type': "{{ user_info.role }}",
                        'X-User': "{{ user_info.user_name }}"
                    },
                    body: JSON.stringify(details)
                })
                .then(response => response.json())
                .then(data => {
                    alert('Corporate data with ' + details.legal_entity_ids + ' ingested.');
                    $legalEntityIds.val("");
                })
                .catch(error => {
                    alert('Error Ingesting Corporate Data');
                })
                .finally(() => {
                    $('.loader').css("visibility", "hidden");
                });
            })
    </script>
</body>
{% endblock %}
