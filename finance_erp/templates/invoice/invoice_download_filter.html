{% extends 'admin/master.html' %}

{% block body %}
<style>
    .loader {
        visibility: hidden;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        margin: auto;
        animation: spin 2s linear infinite;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .error {
        color: red;
        font-size: 1.3rem;
        display: none;
    }

    .required-label::after {
        content: " *";
        color: red;
    }
</style>

<h2>Invoice Download Filters</h2>

<div id="InvoiceFilters">
    <div class="form-group">
        <label for="start_date">Start Date</label>
        <input id="start_date" type="date" class="form-control" placeholder="Select Start Date">
    </div>

    <div class="form-group">
        <label for="end_date">End Date</label>
        <input id="end_date" type="date" class="form-control" placeholder="Select End Date">
    </div>

    <div class="form-group">
        <label for="booker_legal_entity_ids">Booker Profile ID</label>
        <input id="booker_legal_entity_ids" type="text" class="form-control" placeholder="Enter Booker Profile ID" required>
    </div>

    <div class="form-group">
        <label for="invoice_numbers">Invoice Number</label>
        <input id="invoice_numbers" type="text" class="form-control" placeholder="Enter Invoice Number" required>
    </div>

    <div class="form-group">
        <label for="booking_reference_numbers">Booking Reference Number</label>
        <input id="booking_reference_numbers" type="text" class="form-control" placeholder="Enter Booking Reference Number" required>
    </div>

    <div class="form-group">
        <label for="hotel_id">Hotel ID</label>
        <input id="hotel_id" type="text" class="form-control" placeholder="Optional filter for hotel ID">
    </div>

    <div class="form-group">
        <label for="invoice_type">Invoice Type</label>
        <div>
            <input type="checkbox" id="non-credit" name="invoice_type" value="non-credit">
            <label for="non-credit">Non-Credit</label>
        </div>
        <div>
            <input type="checkbox" id="spot-credit" name="invoice_type" value="spot-credit">
            <label for="spot-credit">Spot-Credit</label>
        </div>
        <div>
            <input type="checkbox" id="non-spot-credit" name="invoice_type" value="non-spot-credit">
            <label for="non-spot-credit">Credit</label>
        </div>
        <div>
            <input type="checkbox" id="credit-note" name="invoice_type" value="credit-note">
            <label for="credit-note">Credit Note</label>
        </div>
        <small id="invoiceTypeHelp" class="form-text text-muted">Select one or more invoice types.
            (Leave unselected to include all)</small>
    </div>

    <button id="applyFiltersBtn" class="btn btn-primary">Apply Filters</button>

    <p class="error" id="dateError">Error: The date range should not exceed 180 days.</p>
    <p class="error" id="futureDateError">Error: Dates should not be in the future.</p>
    <p class="error" id="startDateError">Error: Start Date cannot be Greater than End Date.</p>
    <p class="error" id="profileIdError">Error: Booker Profile ID is required.</p>
    <small id="dateNote" class="form-text text-muted">Note: The date range should be within 180 days.</small>
</div>

<div class="loader"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    $(document).ready(function() {

        $('#applyFiltersBtn').on('click', function() {
            let formValid = true;

            const startDate = $('#start_date').val() || null;
            const endDate = $('#end_date').val() || null;
            const bookerProfileIds = $('#booker_legal_entity_ids').val();
            const invoiceNumbers = $('#invoice_numbers').val();
            const bookingReferenceNumbers = $('#booking_reference_numbers').val();
            const hotelId = $('#hotel_id').val();

            let invoiceType = [];
            let fetchCreditNotes = false; // Default to false

            $("input[name='invoice_type']:checked").each(function() {
                const value = $(this).val();

                if (value === "credit-note") {
                    fetchCreditNotes = true; // Set to true if "Credit Note" is selected
                } else {
                    invoiceType.push(value); // Add other selected invoice types to the list
                }
            });

            const currentDate = new Date();

            // Reset all error messages
            $('.error').hide();

            // Validate Start Date and End Date
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffDays = Math.floor((end - start) / (1000 * 60 * 60 * 24));

            if (start > end) {
                $('#startDateError').show();
                formValid = false;
            }

            if (start > currentDate || end > currentDate) {
                $('#futureDateError').show();
                formValid = false;
            }

            if (diffDays > 180) {
                $('#dateError').show();
                formValid = false;
            }

            if (!formValid) return;

            $('.loader').css('visibility', 'visible');

            const details = {
                "start_date": startDate,
                "end_date": endDate,
                "booker_legal_entity_ids": bookerProfileIds.split(',').filter(item => item).map(item => item.trim()),
                "invoice_numbers": invoiceNumbers.split(',').filter(item => item).map(item => item.trim()),
                "booking_reference_numbers": bookingReferenceNumbers.split(',').filter(item => item).map(item => item.trim()),
                "hotel_id": hotelId,
                "invoice_type": invoiceType,
                "fetch_credit_notes": fetchCreditNotes,
                "user_email_id": "{{ admin_email }}"
            };
            $.ajax({
                type: "POST",
                url: "/erp/api/v1/invoice/download",
                data: JSON.stringify(details),
                contentType: 'application/json',
                success: function(response) {
                    alert('Invoices will be delivered to your email.');
                    window.location.reload();
                },
                error: function(xhr) {
                    let errorMessage;
                    if (xhr.responseJSON && xhr.responseJSON.errors && xhr.responseJSON.errors.length > 0) {
                        const errors = xhr.responseJSON.errors;
                        errorMessage = errors?.[0]?.developer_message ?? errors?.[0]?.message ?? 'Error downloading invoices.';
                    } else if (xhr.responseText) {
                        errorMessage = xhr.responseText;
                    }
                    alert(errorMessage);
                },
                complete: function() {
                    $('.loader').css("visibility", "hidden");
                }
            });
        });
    });
</script>

{% endblock %}
