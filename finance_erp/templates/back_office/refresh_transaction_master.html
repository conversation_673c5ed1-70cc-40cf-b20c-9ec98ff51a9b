{% extends 'admin/master.html' %}
{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<body>
<h2>Refresh Transaction Master</h2>
<div id="ManualIng">
    <div class="form-group">
        <label for="hotelId">Hotel Id</label>
        <input id="hotelId" type="text" placeholder="Hotel Id"  class="form-control"  aria-describedby="hotelIdsHelp">
        <small id="hotelIdsHelp" class="form-text text-muted">For Example : 0016581</small>
    </div>
    <button id="AddBtn" class="btn btn-primary">Refresh</button>
</div>
<div class="loader"></div>
    <script>
            var $hotelId = $('#hotelId');
            $('#AddBtn').on('click',function(){
              var displayString = "Please fill ";
              var details = {
                "hotel_id": $hotelId.val().trim()
              }
              if(details.hotel_id.length==0)
              {
                displayString=displayString.concat("Hotel Id")
                alert(displayString);
                return;
              }
              if(details.hotel_id.length>10)
              {
                displayString="Please provide string of length less than 10"
                alert(displayString);
                return;
              }
              $('.loader').css("visibility", "visible");
              fetch('/erp/api/v1/back-office/refresh-transaction-master', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Application': 'FlaskAdminAPIRequest',
                        'X-Auth-Id': "{{ user_info.user_id }}",
                        'X-User-Type': "{{ user_info.role }}",
                        'X-User': "{{ user_info.user_name }}"
                    },
                    body: JSON.stringify(details)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Error while Refreshing Transaction Master');
                    }
                    alert('Transaction Master data for ' + details.hotel_id + ' updated successfully.');
                    $hotelId.val("");
                })
                .catch(error => {
                    alert(error.message);
                })
                .finally(() => {
                    $('.loader').css("visibility", "hidden");
                });
            })
    </script>
</body>
{% endblock %}
