import datetime

from ths_common.utils.id_generator_utils import random_id_generator

from finance_erp.async_job.job.aggregates.job_aggregate import JobAggregate
from finance_erp.async_job.job.dto.job_dto import AsyncJobDTO, ScheduledJobDTO
from finance_erp.async_job.job.entities.job_entity import JobEntity


class JobFactory:
    @staticmethod
    def create_job(job_dto: AsyncJobDTO):
        job_id = random_id_generator("JOB")
        if job_dto.suffix:
            job_id = f"{job_id}-{job_dto.suffix}"
        entity = JobEntity(
            job_name=job_dto.job_name,
            data=job_dto.data,
            eta=None,
            generated_at=datetime.datetime.now(),
            job_id=job_id,
            is_resilient=job_dto.is_resilient,
        )
        if isinstance(job_dto, ScheduledJobDTO):
            entity.eta = job_dto.eta
        elif isinstance(job_dto, AsyncJobDTO):
            entity.eta = datetime.datetime.now()
        else:
            raise TypeError("Unknown job type")
        return JobAggregate(entity)
