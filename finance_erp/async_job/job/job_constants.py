class JobStatus:
    CREATED = "created"
    PROCESSING = "processing"
    FAILED = "failed"
    FINISHED = "finished"
    RESCHEDULED = "rescheduled"
    PICKED_FOR_EXECUTION = "picked_for_execution"


class JobName:
    DATA_PUSH_JOB_NAME = "data_push"
    DATA_PULL_JOB_NAME = "data_pull"
    DATA_INGESTION_JOB_NAME = "data_ingestion"
    DATA_AGGREGATION = "data_aggregation"

    CORPORATE_BILLING_JOB_NAME = "corporate_billing"
    CORPORATE_INVOICE_DISPATCH_JOB_NAME = "corporate_invoice_dispatch"
    CORPORATE_CREDIT_NOTE_DISPATCH_JOB_NAME = "corporate_credit_note_dispatch"
    CORPORATE_STAY_SUMMARY_DISPATCH_JOB_NAME = "corporate_stay_summary_dispatch"

    BACKOFFICE_LEDGER_FILE_GENERATION = "prologic_ledger_file_generation"

    INVOICE_DOWNLOAD_JOB_NAME = "invoice_download"


CONCURRENTLY_EXECUTABLE_JOBS = {
    JobName.DATA_PULL_JOB_NAME,
    JobName.DATA_INGESTION_JOB_NAME,
    JobName.CORPORATE_INVOICE_DISPATCH_JOB_NAME,
    JobName.CORPORATE_CREDIT_NOTE_DISPATCH_JOB_NAME,
    JobName.CORPORATE_STAY_SUMMARY_DISPATCH_JOB_NAME,
    JobName.BACKOFFICE_LEDGER_FILE_GENERATION,
    JobName.CORPORATE_BILLING_JOB_NAME,
    JobName.INVOICE_DOWNLOAD_JOB_NAME,
}
