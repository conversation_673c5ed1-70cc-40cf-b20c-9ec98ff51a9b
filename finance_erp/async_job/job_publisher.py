import datetime
import logging

from finance_erp.async_job.job.job_constants import JobStatus
from finance_erp.async_job.job.repositories.job_repository import JobRepository
from finance_erp.common.decorators import consumer_middleware, session_manager, timed
from finance_erp.infrastructure.messaging.queue_service import BaseQueueService
from object_registry import register_instance

logger = logging.getLogger(__name__)


class JobPublishConfig:
    exchange_type = "topic"
    exchange_name = "finance-erp-job-exchange"
    parallel_job_routing_key = "parallel_job"
    serial_job_routing_key = "serial_job"

    @staticmethod
    def routing_key_for_job(job_entity):
        return (
            JobPublishConfig.parallel_job_routing_key
            if job_entity.is_safe_for_parallel_execution
            else JobPublishConfig.serial_job_routing_key
        )


@register_instance()
class JobPublisher(BaseQueueService):
    def __init__(self):
        super().__init__(JobPublishConfig)


@register_instance(dependencies=[JobRepository, JobPublisher])
class JobPublisherService(object):
    def __init__(self, job_repository, job_publisher: JobPublisher):
        self.job_repository = job_repository
        self.job_publisher = job_publisher

    @consumer_middleware
    @session_manager(commit=True)
    @timed
    def schedule_oldest_unscheduled_job_for_execution(self):
        logger.info("Scheduling oldest unscheduled job of execution")
        job_aggregate = self.job_repository.get_oldest_schedulable_job()
        if not job_aggregate or not job_aggregate.job_entity:
            return False

        logger.info(
            "Scheduling job_name: %s, id: %s",
            job_aggregate.job_entity.job_name,
            job_aggregate.job_entity.job_id,
        )
        self._schedule_for_execution(job_aggregate)
        self.job_repository.update_job(job_aggregate)
        logger.info(
            "Updating the job status for job_name: %s, id: %s to: %s",
            job_aggregate.job_entity.job_name,
            job_aggregate.job_entity.job_id,
            job_aggregate.job_entity.status,
        )
        return job_aggregate.job_entity.status == JobStatus.PROCESSING

    def _schedule_for_execution(self, job_aggregate):
        job_entity = job_aggregate.job_entity
        try:
            self.job_publisher.publish(
                payload=dict(job_id=job_entity.job_id, job_name=job_entity.job_name),
                routing_key=JobPublishConfig.routing_key_for_job(job_entity),
            )
            logger.info(
                "Job job_name: %s, id: %s has been enqueued.",
                job_aggregate.job_entity.job_name,
                job_aggregate.job_entity.job_id,
            )
            job_entity.status = JobStatus.PROCESSING
            job_entity.picked_at = datetime.datetime.utcnow()
            return job_aggregate
        except Exception as e:
            logger.exception(
                "Error while scheduling job with id: %s", job_entity.job_id
            )
            job_entity.status = JobStatus.FAILED
            job_entity.failure_message = str(e)
            return job_aggregate
