import logging
import time

import click
from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.async_job.job_publisher import JobPublisherService
from finance_erp.common.globals import consumer_context
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("async_job_scheduler")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(job_application_service=JobPublisherService)
def async_job_scheduler(
    job_application_service, tenant_id=TenantClient.get_default_tenant()
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id

    failures = 0
    sleep_time = 2
    sleep_time_when_no_job_found = 1
    sleep_time_after_scheduling_job = 0.01
    while True:
        try:
            with current_app.test_request_context():
                logger.info("Initiating job scheduler.")
                jobs_found = (
                    job_application_service.schedule_oldest_unscheduled_job_for_execution()
                )
                success = True
                if not jobs_found:
                    logger.info(
                        "No jobs found. Sleeping for %s seconds until next run.",
                        sleep_time_when_no_job_found,
                    )
                    time.sleep(sleep_time_when_no_job_found)
                    continue
        except Exception as ex:
            logger.exception("Error occurred while scheduling job")
            success = False

        if not success:
            failures += 1
        else:
            failures = 0
            sleep_time = 2

        if failures > 3:
            logger.critical("Job publishing failed for the %s th time", failures)
            if sleep_time < 64:
                sleep_time = sleep_time * 2
            time.sleep(sleep_time)
        else:
            logger.info(
                "Job found. Sleeping for %s seconds", sleep_time_after_scheduling_job
            )
            time.sleep(sleep_time_after_scheduling_job)
