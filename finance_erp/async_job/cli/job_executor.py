import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.async_job.job.repositories.job_repository import JobRepository
from finance_erp.async_job.job_consumer import JobConsumer
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.common.globals import consumer_context
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from object_registry import inject


@click.command("async_job_executor")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    "--consume-serial-job", is_flag=True, help="Explicitly enable consume serial jobs"
)
@with_appcontext
@inject(
    job_registry=JobRegistry,
    slack_alert_client=SlackAlertServiceClient,
    job_repository=JobRepository,
)
def async_job_executor(
    job_registry: JobRegistry,
    slack_alert_client: SlackAlertServiceClient,
    job_repository: JobRepository,
    consume_serial_job: bool,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    consumer = JobConsumer(
        job_registry=job_registry,
        slack_alert_client=slack_alert_client,
        job_repository=job_repository,
        can_consume_serial_jobs=consume_serial_job,
        tenant_id=tenant_id,
    )
    consumer.start_consumer()
