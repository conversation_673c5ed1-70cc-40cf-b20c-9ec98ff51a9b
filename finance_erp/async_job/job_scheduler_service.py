import logging
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.async_job.job.dto.job_dto import Async<PERSON>obD<PERSON>, ScheduledJobDTO
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job.job_factory import JobFactory
from finance_erp.async_job.job.repositories.job_repository import JobRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[JobRepository])
class JobSchedulerService:
    def __init__(self, job_repository: JobRepository):
        self.job_repository = job_repository

    def schedule(self, job_dto: AsyncJobDTO):
        job_aggregate = JobFactory.create_job(job_dto)
        self.job_repository.save(job_aggregate)
        logger.info("Created a job with id: %s", job_aggregate.job_entity.job_id)
        return job_aggregate

    def batch_schedule(self, job_dtos: List[AsyncJobDTO]):
        job_aggregates = [JobFactory.create_job(job_dto) for job_dto in job_dtos]
        self.job_repository.insert_many(job_aggregates)

    def create_data_push_job(self, report_name, data_push, event_id, eta=None):
        return self._create_report_job(
            JobName.DATA_PUSH_JOB_NAME, report_name, data_push, event_id, eta
        )

    def create_data_aggregation_job(self, report_name, data, event_id):
        return self._create_report_job(
            JobName.DATA_AGGREGATION, report_name, data, event_id, eta=None
        )

    def create_data_pull_job(self, report_name, data_push, event_id, eta=None):
        return self._create_report_job(
            JobName.DATA_PULL_JOB_NAME, report_name, data_push, event_id, eta
        )

    def create_data_ingestion_job(
        self, report_name, data_push, event_id, eta=None, is_resilient=False
    ):
        return self._create_report_job(
            JobName.DATA_INGESTION_JOB_NAME,
            report_name,
            data_push,
            event_id,
            eta,
            is_resilient,
        )

    def _create_report_job(
        self, job_name, report_name, data, event_id, eta=None, is_resilient=False
    ):
        if eta:
            job_dto = ScheduledJobDTO(
                job_name=job_name,
                data=dict(
                    event_id=event_id, request_data=data, report_name=report_name
                ),
                eta=eta,
                is_resilient=is_resilient,
            )
        else:
            job_dto = AsyncJobDTO(
                job_name=job_name,
                data=dict(
                    event_id=event_id,
                    request_data=data,
                    report_name=report_name,
                ),
                is_resilient=is_resilient,
            )
        return self.schedule(job_dto)

    def schedule_corporate_billing_job(self, corporate_id: str, billing_date: str):
        job_dto = AsyncJobDTO(
            job_name=JobName.CORPORATE_BILLING_JOB_NAME,
            data=dict(corporate_id=corporate_id, billing_date=billing_date),
            is_resilient=True,
        )
        return self.schedule(job_dto)

    def schedule_corporate_invoice_dispatch_job(self, invoice_id: str):
        job_dto = ScheduledJobDTO(
            job_name=JobName.CORPORATE_INVOICE_DISPATCH_JOB_NAME,
            data=dict(invoice_id=invoice_id),
            eta=dateutils.add(dateutils.current_datetime(), minutes=1),
        )
        return self.schedule(job_dto)

    def schedule_corporate_credit_note_dispatch_job(self, credit_note_id: str):
        job_dto = ScheduledJobDTO(
            job_name=JobName.CORPORATE_CREDIT_NOTE_DISPATCH_JOB_NAME,
            data=dict(credit_note_id=credit_note_id),
            eta=dateutils.add(dateutils.current_datetime(), minutes=1),
        )
        return self.schedule(job_dto)

    def schedule_corporate_stay_summary_dispatch_job(
        self, stay_summary_id: str, corporate_id: str = None
    ):
        job_dto = AsyncJobDTO(
            job_name=JobName.CORPORATE_STAY_SUMMARY_DISPATCH_JOB_NAME,
            data=dict(corporate_id=corporate_id, stay_summary_id=stay_summary_id),
            is_resilient=True,
        )
        return self.schedule(job_dto)

    def schedule_backoffice_ledger_generation_job(
        self,
        date: str,
        hotel_id: str,
        erp_name: str,
        refresh_transaction_master: bool = False,
    ):
        job_dto = AsyncJobDTO(
            job_name=JobName.BACKOFFICE_LEDGER_FILE_GENERATION,
            data=dict(
                date=date,
                hotel_id=hotel_id,
                refresh_transaction_master=refresh_transaction_master,
                erp_name=erp_name,
            ),
        )
        return self.schedule(job_dto)

    def schedule_download_invoice_job(
        self,
        start_date: str,
        end_date: str,
        booker_legal_entity_ids: List[str],
        invoice_numbers: List[str],
        booking_reference_numbers: List[str],
        hotel_id: str,
        invoice_type: str,
        fetch_credit_notes: bool,
        user_email_id: str,
    ):
        job_dto = AsyncJobDTO(
            job_name=JobName.INVOICE_DOWNLOAD_JOB_NAME,
            data=dict(
                start_date=start_date,
                end_date=end_date,
                booker_legal_entity_ids=booker_legal_entity_ids,
                invoice_numbers=invoice_numbers,
                booking_reference_numbers=booking_reference_numbers,
                hotel_id=hotel_id,
                invoice_type=invoice_type,
                fetch_credit_notes=fetch_credit_notes,
                user_email_id=user_email_id,
            ),
        )
        return self.schedule(job_dto)
