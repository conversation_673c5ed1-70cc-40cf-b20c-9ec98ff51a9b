from typing import Dict

from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.corporate.command_handler.push_corporate_report import (
    BulkPushCorporateReportCommandHandler,
)
from finance_erp.application.hotel.command_handler.push_hotel_report import (
    BulkPushHotelReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.push_purchase_report import (
    BulkPushPurchaseReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.push_sales_summary_report import (
    BulkPushSalesSummaryReportCommandHandler,
)
from finance_erp.application.ota.commands.push_ota_report import (
    BulkPushOTACommissionReportCommandHandler,
)
from finance_erp.application.payments.commands.push_payment_summary_report import (
    BulkPushPaymentReportCommandHandler,
)
from finance_erp.application.settlement.expense.commands.push_expense_report import (
    BulkPushExpenseReportCommandHandler,
)
from finance_erp.application.settlement.hotel_adjustment.commands.push_hotel_adjustment_report import (
    BulkPushHotelAdjustmentReportCommandHandler,
)
from finance_erp.application.settlement.loan.commands.push_loan_report import (
    BulkPushLoanReportCommandHandler,
)
from finance_erp.application.settlement.tax.commands.push_tax_report import (
    BulkPushTaxReportCommandHandler,
)
from finance_erp.application.settlement.treebo_fee.commands.push_treebo_fee_report import (
    BulkPushTreeboFeeReportCommandHandler,
)
from finance_erp.application.ta_commission.commands.push_ta_commission_report import (
    BulkPushTACommissionReportCommandHandler,
)
from finance_erp.async_job.handlers.base_handler import BaseGenericJobHandler
from finance_erp.common.constants import NavisionReports
from object_registry import locate_instance, register_instance


@register_instance()
class DataPushGenericJobHandler(BaseGenericJobHandler):
    def _get_handler_map(self):
        handler_function_map: Dict[str, BaseDataPushCommandHandler] = {
            NavisionReports.PURCHASE_INVOICE_REPORT: locate_instance(
                BulkPushPurchaseReportCommandHandler
            ),
            NavisionReports.PAYMENT_GATEWAY_REPORT: locate_instance(
                BulkPushPaymentReportCommandHandler
            ),
            NavisionReports.CORPORATE_REPORT: locate_instance(
                BulkPushCorporateReportCommandHandler
            ),
            NavisionReports.HOTEL_REPORT: locate_instance(
                BulkPushHotelReportCommandHandler
            ),
            NavisionReports.TREEBO_FEE_REPORT: locate_instance(
                BulkPushTreeboFeeReportCommandHandler
            ),
            NavisionReports.EXPENSE_REPORT: locate_instance(
                BulkPushExpenseReportCommandHandler
            ),
            NavisionReports.TAX_REPORT: locate_instance(
                BulkPushTaxReportCommandHandler
            ),
            NavisionReports.LOAN_REPORT: locate_instance(
                BulkPushLoanReportCommandHandler
            ),
            NavisionReports.HOTEL_ADJUSTMENT_REPORT: locate_instance(
                BulkPushHotelAdjustmentReportCommandHandler
            ),
            NavisionReports.CUSTOMER_INVOICE_SUMMARY_REPORT: locate_instance(
                BulkPushSalesSummaryReportCommandHandler
            ),
            NavisionReports.OTA_COMMISSION_REPORT: locate_instance(
                BulkPushOTACommissionReportCommandHandler
            ),
            NavisionReports.TA_COMMISSION_REPORT: locate_instance(
                BulkPushTACommissionReportCommandHandler
            ),
        }
        return handler_function_map
