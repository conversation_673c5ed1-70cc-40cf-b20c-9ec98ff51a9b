from finance_erp.application.invoice.command_handler.generate_sales_summary import (
    SalesInvoiceSummaryGenerationHandler,
)
from finance_erp.application.payments.commands.generate_payment_summary import (
    PaymentSummaryGenerationHandler,
)
from finance_erp.async_job.handlers.base_handler import BaseGenericJobHandler
from finance_erp.common.constants import NavisionReports
from object_registry import locate_instance, register_instance


@register_instance()
class DataAggregationGenericJobHandler(BaseGenericJobHandler):
    def _get_handler_map(self):
        handler_function_map = {
            NavisionReports.PAYMENT_GATEWAY_SUMMARY_REPORT: locate_instance(
                PaymentSummaryGenerationHandler
            ),
            NavisionReports.CUSTOMER_INVOICE_SUMMARY_REPORT: locate_instance(
                SalesInvoiceSummaryGenerationHandler
            ),
        }
        return handler_function_map
