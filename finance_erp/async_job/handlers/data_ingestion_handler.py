from typing import Dict

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.application.corporate.command_handler.bulk_ingest_corporate_report import (
    BulkIngestCorporateReportCommandHandler,
)
from finance_erp.application.corporate.command_handler.ingest_corporate_from_cp import (
    CPCorporateDataIngestionHandler,
)
from finance_erp.application.crs.command_handlers.ingest_booking import (
    CrsBookingIngestionCommandHandler,
)
from finance_erp.application.hotel.command_handler.bulk_ingest_hotel_report import (
    BulkIngestHotelReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.bulk_ingest_purchase_report import (
    BulkIngestPurchaseReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.bulk_ingest_sales_report import (
    BulkIngestSalesReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_credit_note import (
    CreditNoteIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_invoice import (
    InvoiceIngestionCommandHandler,
)
from finance_erp.application.ota.commands.bulk_ingest_ota_report import (
    BulkIngestOTACommissionReportCommandHandler,
)
from finance_erp.application.payments.commands.bulk_ingest_payment_report import (
    BulkIngestPaymentReportCommandHandler,
)
from finance_erp.application.pos.commands.bulk_ingest_pos_revenue import (
    BulkIngestPOSRevenueCommandHandler,
)
from finance_erp.application.settlement.expense.commands.bulk_ingest_expense_report import (
    BulkIngestExpenseReportCommandHandler,
)
from finance_erp.application.settlement.hotel_adjustment.commands.bulk_ingest_hotel_adjustment_report import (
    BulkIngestHotelAdjustmentReportCommandHandler,
)
from finance_erp.application.settlement.loan.commands.bulk_ingest_loan_report import (
    BulkIngestLoanReportCommandHandler,
)
from finance_erp.application.settlement.tax.commands.bulk_ingest_tax_report import (
    BulkIngestTaxReportCommandHandler,
)
from finance_erp.application.settlement.treebo_fee.commands.bulk_ingest_treebo_fee_report import (
    BulkIngestTreeboFeeReportCommandHandler,
)
from finance_erp.application.ta_commission.commands.bulk_ingest_ta_commission_report import (
    BulkIngestTACommissionReportCommandHandler,
)
from finance_erp.async_job.handlers.base_handler import BaseGenericJobHandler
from finance_erp.common.constants import EntityTypes, IngestionJobs, NavisionReports
from object_registry import locate_instance, register_instance


@register_instance()
class DataIngestGenericJobHandler(BaseGenericJobHandler):
    def _get_handler_map(self):
        handler_function_map: Dict[str, BaseIngestionCommandHandler] = {
            NavisionReports.PURCHASE_INVOICE_REPORT: locate_instance(
                BulkIngestPurchaseReportCommandHandler
            ),
            NavisionReports.PAYMENT_GATEWAY_REPORT: locate_instance(
                BulkIngestPaymentReportCommandHandler
            ),
            NavisionReports.CORPORATE_REPORT: locate_instance(
                BulkIngestCorporateReportCommandHandler
            ),
            NavisionReports.HOTEL_REPORT: locate_instance(
                BulkIngestHotelReportCommandHandler
            ),
            NavisionReports.TREEBO_FEE_REPORT: locate_instance(
                BulkIngestTreeboFeeReportCommandHandler
            ),
            NavisionReports.EXPENSE_REPORT: locate_instance(
                BulkIngestExpenseReportCommandHandler
            ),
            NavisionReports.LOAN_REPORT: locate_instance(
                BulkIngestLoanReportCommandHandler
            ),
            NavisionReports.TAX_REPORT: locate_instance(
                BulkIngestTaxReportCommandHandler
            ),
            NavisionReports.HOTEL_ADJUSTMENT_REPORT: locate_instance(
                BulkIngestHotelAdjustmentReportCommandHandler
            ),
            NavisionReports.CUSTOMER_INVOICE_REPORT: locate_instance(
                BulkIngestSalesReportCommandHandler
            ),
            NavisionReports.OTA_COMMISSION_REPORT: locate_instance(
                BulkIngestOTACommissionReportCommandHandler
            ),
            NavisionReports.TA_COMMISSION_REPORT: locate_instance(
                BulkIngestTACommissionReportCommandHandler
            ),
            IngestionJobs.INVOICE_INGESTION: locate_instance(
                InvoiceIngestionCommandHandler
            ),
            IngestionJobs.CREDIT_NOTE_INGESTION: locate_instance(
                CreditNoteIngestionCommandHandler
            ),
            IngestionJobs.BOOKING_INGESTION: locate_instance(
                CrsBookingIngestionCommandHandler
            ),
            IngestionJobs.CP_CORPORATE_INGESTION: locate_instance(
                CPCorporateDataIngestionHandler
            ),
            EntityTypes.POS_REVENUE: locate_instance(
                BulkIngestPOSRevenueCommandHandler
            ),
        }
        return handler_function_map
