import logging

from object_registry import register_instance

RETRY_DELAY = 5
MAX_RETRY = 3
MAX_RETRY_FOR_RESILIENT_JOBS = 4

logger = logging.getLogger(__name__)


@register_instance()
class BaseGenericJobHandler:
    def handle(self, **job_data):
        handler_function_map = self._get_handler_map()
        handler_function_map[
            job_data.get("report_name")
        ].handle_request_from_job_executor(**job_data)
        return True

    def _get_handler_map(self):
        raise NotImplementedError()
