from typing import Dict

from finance_erp.application.common.base_data_pull_handler import (
    BaseDataPullCommandHandler,
)
from finance_erp.application.corporate.command_handler.pull_corporate_report import (
    BulkPullCorporateReportCommandHandler,
)
from finance_erp.application.hotel.command_handler.pull_hotel_report import (
    BulkPullHotelReportCommandHandler,
)
from finance_erp.async_job.handlers.base_handler import BaseGenericJobHandler
from finance_erp.common.constants import NavisionReports
from object_registry import locate_instance, register_instance


@register_instance()
class DataPullGenericJobHandler(BaseGenericJobHandler):
    def _get_handler_map(self):
        handler_function_map: Dict[str, BaseDataPullCommandHandler] = {
            NavisionReports.CORPORATE_REPORT: locate_instance(
                BulkPullCorporateReportCommandHandler
            ),
            NavisionReports.HOTEL_REPORT: locate_instance(
                BulkPullHotelReportCommandHandler
            ),
        }
        return handler_function_map
