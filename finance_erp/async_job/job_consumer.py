import logging
import traceback
from datetime import datetime, timedelta

from flask import current_app
from flask.cli import with_appcontext
from ths_common.exceptions import DatabaseLockError
from treebo_commons.multitenancy.tenant_client import TenantClient

from finance_erp.async_job.job.aggregates.job_aggregate import JobAggregate
from finance_erp.async_job.job.job_constants import JobStatus
from finance_erp.async_job.job.repositories.job_repository import JobRepository
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_result_dto import JobResultDto
from finance_erp.common.decorators import consumer_middleware, session_manager
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.consumers.base_consumer import BaseRMQConsumer
from finance_erp.infrastructure.consumers.consumer_config import JobConsumerConfig

logger = logging.getLogger(__name__)


class JobConsumer(BaseRMQConsumer):
    def __init__(
        self,
        job_registry: JobRegistry,
        slack_alert_client: SlackAlertServiceClient,
        job_repository: JobRepository,
        can_consume_serial_jobs: bool = False,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(
            JobConsumerConfig(
                can_consume_serial_jobs=can_consume_serial_jobs,
                tenant_id=tenant_id,
            )
        )
        self.tenant_id = tenant_id
        logger.info(
            "Listening to RMQ on host: %s from queues: %s",
            self.connection,
            [queue.name for queue in self.queues],
        )
        self.job_registry = job_registry
        self.slack_alert_client = slack_alert_client
        self.job_repository = job_repository

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        logger.debug("Processing messages from Job Queue")

        with current_app.test_request_context():
            try:
                job_id = body["job_id"]
                logger.info("Executing job with id: %s", job_id)
                job_aggregate = self.job_repository.get_job(job_id, with_lock=True)
                if job_aggregate.job_entity.status != JobStatus.PROCESSING:
                    logger.info(
                        "Job status is not valid for execution: %s. Skipping execution",
                        job_aggregate.job_entity.status,
                    )
                    return
                self.mark_picked_for_execution(job_aggregate)
                self.process_job(job_aggregate)
            except Exception as e:
                logger.exception("Job consumer error while processing message: %s", e)
                self.slack_alert_client.send_alert(
                    f"Job consumer error while processing message: {str(e)}"
                )

    @session_manager(commit=True)
    def process_job(
        self,
        job_aggregate: JobAggregate,
    ):
        job_entity = job_aggregate.job_entity
        job_aggregate.increment_job_run()
        job_result_dto: JobResultDto = self.handle_execution(job_entity)
        self.update_job(job_aggregate, job_result_dto)

    def handle_execution(self, job_entity):
        try:
            handler_function = self.job_registry.get_executor(job_entity.job_name)
            job_data = job_entity.data or {}
            handler_function(**job_data)
            return JobResultDto()
        except Exception as e:
            should_retry = job_entity.should_retry(e)
            retry_at_eta = job_entity.derive_next_retry_time(e)
            failure = f"Failed {str(traceback.format_exc())}"
            if isinstance(e, DatabaseLockError):
                should_retry = True
                retry_at_eta = datetime.now() + timedelta(minutes=2)
            logger.exception("Job failed: reschedule =  %s", should_retry)
            return JobResultDto(
                run_successful=False,
                should_retry=should_retry,
                retry_at_eta=retry_at_eta,
                remarks=failure,
                send_alert_after_retry_threshold=getattr(
                    e, "send_alert_after_retry_threshold", True
                ),
            )

    @session_manager(commit=True)
    def mark_picked_for_execution(self, job_aggregate):
        job_aggregate.mark_picked_for_execution()
        self.job_repository.update_job(job_aggregate)

    def update_job(
        self,
        job_aggregate,
        job_result_dto,
    ):
        if job_result_dto:
            logger.info(
                "Job execution status for job id: %s -> %s",
                job_aggregate.job_entity.job_id,
                job_result_dto.run_successful,
            )
            if not job_result_dto.run_successful:
                if job_result_dto.should_retry:
                    job_aggregate.reschedule(
                        job_result_dto.retry_at_eta, reason=job_result_dto.remarks
                    )
                else:
                    job_aggregate.failed(job_result_dto.remarks)
                    if job_result_dto.send_alert_after_retry_threshold:
                        self.slack_alert_client.record_event(
                            event_type="job_failure", event_payload=job_aggregate
                        )
            else:
                job_aggregate.update_status(success=True)
        else:
            logger.info(
                "Job execution status for job id: %s -> %s",
                job_aggregate.job_entity.job_id,
                True,
            )
            job_aggregate.update_status(success=True)
        self.job_repository.update_job(job_aggregate)
