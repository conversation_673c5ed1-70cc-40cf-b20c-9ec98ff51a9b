0000000000000000000000000000000000000000 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744652722 +0530	branch: Created from HEAD
111d7921427d163fd8f456d605d359d664f0bd9d a89f68c865c22b6b2b3a65e051dac00a0d77b806 arvind-a-1 <<EMAIL>> 1744652783 +0530	commit: updated for ota
a89f68c865c22b6b2b3a65e051dac00a0d77b806 e0e4317cf33f63d76584d5e5a88903b642a39598 arvind-a-1 <<EMAIL>> 1744652816 +0530	commit: updated for payment
e0e4317cf33f63d76584d5e5a88903b642a39598 5ba021e1f1da1d9b642747a6940f95e7f346846d arvind-a-1 <<EMAIL>> 1744652849 +0530	commit: updated for reseller
5ba021e1f1da1d9b642747a6940f95e7f346846d 020abec9ab64c62f8a91538dd2d5361608ab1f6a arvind-a-1 <<EMAIL>> 1744652898 +0530	commit: updated for expense
020abec9ab64c62f8a91538dd2d5361608ab1f6a 94603f2c51b4f90514eccd70b09f6ce316216d36 arvind-a-1 <<EMAIL>> 1744652931 +0530	commit: updated for settlement
94603f2c51b4f90514eccd70b09f6ce316216d36 e982cdd3d95c561f61f23b0aa660b4cab89303c2 arvind-a-1 <<EMAIL>> 1744652962 +0530	commit: updated common entity
e982cdd3d95c561f61f23b0aa660b4cab89303c2 90dd9378552b3b8f8bdc6fcd5a33681ef00986dd arvind-a-1 <<EMAIL>> 1744652986 +0530	commit: updated db fields
90dd9378552b3b8f8bdc6fcd5a33681ef00986dd ea17c8980d83579b95481ab0ce2eb1217e6d34ef arvind-a-1 <<EMAIL>> 1744797185 +0530	commit: updated for pydantic parser
ea17c8980d83579b95481ab0ce2eb1217e6d34ef 62d50b6d4d82f5ca85a06f1459f5ab393620a15f arvind-a-1 <<EMAIL>> 1744797215 +0530	commit: updated schema
62d50b6d4d82f5ca85a06f1459f5ab393620a15f 8e2819e6693c3b51b9cde000e6c1cf3aa536910f arvind-a-1 <<EMAIL>> 1744797247 +0530	commit: updated exprense schema
8e2819e6693c3b51b9cde000e6c1cf3aa536910f b61e5540e33c67c32367faa12f5f3caefd2eb0a2 arvind-a-1 <<EMAIL>> 1744797297 +0530	commit: updated common exprense schema
b61e5540e33c67c32367faa12f5f3caefd2eb0a2 4923949449dee76dbf86bea29ba716a2c77c91d1 arvind-a-1 <<EMAIL>> 1744797309 +0530	commit: updated requirements
4923949449dee76dbf86bea29ba716a2c77c91d1 17ba8a97ce9986060fee2783a24ebca32cb23fc6 arvind-a-1 <<EMAIL>> 1745296723 +0530	commit: updated request parser for pydantic
17ba8a97ce9986060fee2783a24ebca32cb23fc6 c6073ebc0a4f40d9f916336cb3279daea6826bbe arvind-a-1 <<EMAIL>> 1745296747 +0530	commit: updated db layer
c6073ebc0a4f40d9f916336cb3279daea6826bbe 47cdf5b9dfe21f5b31e3f3354bd0ab4fd6e83ff7 arvind-a-1 <<EMAIL>> 1745296829 +0530	commit: updated domain layer
47cdf5b9dfe21f5b31e3f3354bd0ab4fd6e83ff7 6542beb892d79651fff204a18dc81bd4afa9e43c arvind-a-1 <<EMAIL>> 1745296986 +0530	commit: updated schema to pydantic
6542beb892d79651fff204a18dc81bd4afa9e43c 2aaa311e3fab0ac1c518e32d9edf5ae09bc437af arvind-a-1 <<EMAIL>> 1745297042 +0530	commit: updated schemas
2aaa311e3fab0ac1c518e32d9edf5ae09bc437af 393ba5f71ea54bcdcf41a61be81a42980c9266a0 arvind-a-1 <<EMAIL>> 1745297074 +0530	commit: updated api for pydantic
393ba5f71ea54bcdcf41a61be81a42980c9266a0 7f906598a3732d57acda1cb7eb7c8291cf03802e arvind-a-1 <<EMAIL>> 1745297102 +0530	commit: updated app.py
7f906598a3732d57acda1cb7eb7c8291cf03802e 19bca4fbca1d42090a94a5d73d01438df5a7d9f2 arvind-a-1 <<EMAIL>> 1745297131 +0530	commit: updated application layer
19bca4fbca1d42090a94a5d73d01438df5a7d9f2 28c939d0e106bbd427d14d0af83f9573ff7a338b arvind-a-1 <<EMAIL>> 1745297187 +0530	commit: updated requirements for pydantic
28c939d0e106bbd427d14d0af83f9573ff7a338b 2dbbd5ac4e8ee68d3621fb9946066d915b43680b arvind-a-1 <<EMAIL>> 1745342254 +0530	commit: updated requests schema
2dbbd5ac4e8ee68d3621fb9946066d915b43680b 56c7468f493d81ed56f0f5348edb6fbc670ac694 arvind-a-1 <<EMAIL>> 1745342296 +0530	commit: updated common schema
56c7468f493d81ed56f0f5348edb6fbc670ac694 b3bddbcd2c3aafb33cf4839e576d7d612b48a58e arvind-a-1 <<EMAIL>> 1745342327 +0530	commit: Updated domain layer
b3bddbcd2c3aafb33cf4839e576d7d612b48a58e 9453b96b7061f82b9e7eea0622da6fbb58aa61c5 arvind-a-1 <<EMAIL>> 1745342357 +0530	commit: updated api layer
9453b96b7061f82b9e7eea0622da6fbb58aa61c5 0c26fe4f067cd03da376a947f42901a239c64357 arvind-a-1 <<EMAIL>> 1745342373 +0530	commit: updated db file
0c26fe4f067cd03da376a947f42901a239c64357 795f29736a95e50146832ffb897bd7655316fa88 arvind-a-1 <<EMAIL>> 1745342447 +0530	commit: updated application layer
795f29736a95e50146832ffb897bd7655316fa88 6c8913f2002d9b5bd9b1ebb4d846f3a64b340776 arvind-a-1 <<EMAIL>> 1745342462 +0530	commit: updated middleware
6c8913f2002d9b5bd9b1ebb4d846f3a64b340776 f3e0a6dd16970fa706d84ee41ae6c21aff79be99 arvind-a-1 <<EMAIL>> 1745342489 +0530	commit: updated test cases
f3e0a6dd16970fa706d84ee41ae6c21aff79be99 fa5256329bc7540d060d75364bf5d26a92c2e175 arvind-a-1 <<EMAIL>> 1745342873 +0530	pull origin fix/PROM-18381: Fast-forward
fa5256329bc7540d060d75364bf5d26a92c2e175 e05525655e984d21bd911572565f3c2eebc426e1 arvind-a-1 <<EMAIL>> 1745343122 +0530	commit: fixed black and isort format
e05525655e984d21bd911572565f3c2eebc426e1 04b517889b0c1e52d311637912d42679f4fdbcd1 arvind-a-1 <<EMAIL>> 1745343460 +0530	commit: updated requirements
04b517889b0c1e52d311637912d42679f4fdbcd1 6776d411864882cdaf17e6e85778257893f58de8 arvind-a-1 <<EMAIL>> 1745411769 +0530	commit: updated api layer
6776d411864882cdaf17e6e85778257893f58de8 46f51508716f43fa4e6d148caa6b3e4321a3f8d8 arvind-a-1 <<EMAIL>> 1745411794 +0530	commit: updated schema
46f51508716f43fa4e6d148caa6b3e4321a3f8d8 f392c1051d318e61d684758734d735b12af42a49 arvind-a-1 <<EMAIL>> 1745411823 +0530	commit: updated pip version to 24.1
f392c1051d318e61d684758734d735b12af42a49 **************************************** arvind-a-1 <<EMAIL>> 1745470492 +0530	commit: fix for sonarqube function length
**************************************** 49171dfe8691f8db8731cb9dfded96d0a7e9de28 arvind-a-1 <<EMAIL>> 1745855938 +0530	commit: updated entities to pydantic
49171dfe8691f8db8731cb9dfded96d0a7e9de28 83ce19ef4bf82f4426a4bc2cebb7b8808cfb2d7a arvind-a-1 <<EMAIL>> 1745855971 +0530	commit: updated api with helper function
83ce19ef4bf82f4426a4bc2cebb7b8808cfb2d7a 7bbf919c304a8812166f9ad8afc4b1ff4f6b9a1e arvind-a-1 <<EMAIL>> 1745855993 +0530	commit: updated application layer
7bbf919c304a8812166f9ad8afc4b1ff4f6b9a1e c287236b2530ed1019a1c98a9112227d0ac37ace arvind-a-1 <<EMAIL>> 1745856011 +0530	commit: updated schema
c287236b2530ed1019a1c98a9112227d0ac37ace 276a2d543d224ef017c91e4e95d91a92779cf14c arvind-a-1 <<EMAIL>> 1745856036 +0530	commit: updated db files
276a2d543d224ef017c91e4e95d91a92779cf14c 1619942a60569ed91b98f69e1adb273ca0b13937 arvind-a-1 <<EMAIL>> 1745924380 +0530	commit: updated schemas and models to pydantic
1619942a60569ed91b98f69e1adb273ca0b13937 1dd015f58d3fd71eb9348136a1abf83ab9157685 arvind-a-1 <<EMAIL>> 1745924419 +0530	commit: removed marshmallow from requirements
1dd015f58d3fd71eb9348136a1abf83ab9157685 1b9ae8402d47c2f2b29641ef4173d72e712dd30a arvind-a-1 <<EMAIL>> 1746446868 +0530	commit: moved all dataclasses to pydantic schemas
1b9ae8402d47c2f2b29641ef4173d72e712dd30a 101c343d8a403da9a669b72761f5ad899965ff56 arvind-a-1 <<EMAIL>> 1746446892 +0530	commit: updated tds_per field
101c343d8a403da9a669b72761f5ad899965ff56 66f217604826737d238efe50fcf6eac56c91e826 arvind-a-1 <<EMAIL>> 1746446932 +0530	commit: updated api layer
66f217604826737d238efe50fcf6eac56c91e826 1ace176cdaa9b2a72fc67a6a1c1749a18a03e221 arvind-a-1 <<EMAIL>> 1746446952 +0530	commit: updated dto and command handler
1ace176cdaa9b2a72fc67a6a1c1749a18a03e221 e15647f0494037f178fef3bac16251d6284e5685 arvind-a-1 <<EMAIL>> 1746446976 +0530	commit: updated job entity schema
e15647f0494037f178fef3bac16251d6284e5685 1f8042c4590f0c567781351b5da2d1010dc8ef6b arvind-a-1 <<EMAIL>> 1748947026 +0530	pull: Fast-forward
1f8042c4590f0c567781351b5da2d1010dc8ef6b 90b0ed49484efd8682a6a55536c53dfcc862c3d3 arvind-a-1 <<EMAIL>> 1748950371 +0530	commit: updated schema to pydantic
90b0ed49484efd8682a6a55536c53dfcc862c3d3 35ad8475b3403993498a5c9072b7f46bff5432e3 arvind-a-1 <<EMAIL>> 1748950444 +0530	commit: updated entites
35ad8475b3403993498a5c9072b7f46bff5432e3 d4a616a4bc33201324e8244e14431a538fadbe84 arvind-a-1 <<EMAIL>> 1748950460 +0530	commit: updated for ota
d4a616a4bc33201324e8244e14431a538fadbe84 9f89499d2be597cf52f6f038b6951e229cd4c73a arvind-a-1 <<EMAIL>> 1748950482 +0530	commit: updated tds_percentage for ota
9f89499d2be597cf52f6f038b6951e229cd4c73a 6b1c907a8e10417f29935e013b54d80eeb3d8c60 arvind-a-1 <<EMAIL>> 1748950755 +0530	commit: updated for tds_percentage
6b1c907a8e10417f29935e013b54d80eeb3d8c60 1e2dd4ec39dc8b50d2ed0a042a72d1cfcf4b3b8e arvind-a-1 <<EMAIL>> 1748956519 +0530	commit: updated for tds_percentage
1e2dd4ec39dc8b50d2ed0a042a72d1cfcf4b3b8e 214f7d73fe74c71c7accab60b93c190fa09c2641 arvind-a-1 <<EMAIL>> 1748956552 +0530	commit: updated schema for ta commission
214f7d73fe74c71c7accab60b93c190fa09c2641 d67402d56342f9495b92c8976134fa357746f5f9 arvind-a-1 <<EMAIL>> 1748956569 +0530	commit: updated db file
d67402d56342f9495b92c8976134fa357746f5f9 c1a2875482b1964b72043c1360272ba838d383b7 arvind-a-1 <<EMAIL>> 1752718068 +0530	pull origin fix/PROM-18381: Fast-forward
c1a2875482b1964b72043c1360272ba838d383b7 9ca0f1b931cbbc9c4074eb0ae50d5004fe570d44 arvind-a-1 <<EMAIL>> 1752720593 +0530	commit: fix requirements
9ca0f1b931cbbc9c4074eb0ae50d5004fe570d44 5707b4d05808229e51c2ffc30b0a94eef393af14 arvind-a-1 <<EMAIL>> 1752720615 +0530	commit: updated none entry
5707b4d05808229e51c2ffc30b0a94eef393af14 cde12e140091f76d18cfa73c0dd2b31f65fc2ad9 arvind-a-1 <<EMAIL>> 1752721651 +0530	commit: updated requirements for the project
