[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = **************:treebo-noss/finance_erp.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[branch "PROM-18245-booker-profile-id-comma-separated"]
	remote = origin
	merge = refs/heads/PROM-18245-booker-profile-id-comma-separated
[branch "feat/PROM-18374"]
	remote = origin
	merge = refs/heads/feat/PROM-18374
	vscode-merge-base = origin/main
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
	vscode-merge-base = origin/develop
[branch "fix/PROM-18381"]
	remote = origin
	merge = refs/heads/fix/PROM-18381
[branch "feat/PROM-18569-invoice-booking-reference-filters-bulk-download"]
	remote = origin
	merge = refs/heads/feat/PROM-18569-invoice-booking-reference-filters-bulk-download
[branch "PROM-18569"]
	remote = origin
	merge = refs/heads/PROM-18569
[branch "patch-PROM-18381"]
	remote = origin
	merge = refs/heads/patch-PROM-18381
[branch "hotfix-credit-note"]
	remote = origin
	merge = refs/heads/hotfix-credit-note
[branch "hotfix-corporate-dto"]
	remote = origin
	merge = refs/heads/hotfix-corporate-dto
[branch "hotfix-ledger-control"]
	remote = origin
	merge = refs/heads/hotfix-ledger-control
[branch "hotfix-pydantic-models"]
	remote = origin
	merge = refs/heads/hotfix-pydantic-models
[branch "hotfix-cl-ledger-item"]
	remote = origin
	merge = refs/heads/hotfix-cl-ledger-item
[branch "hotfix-session-fix"]
	remote = origin
	merge = refs/heads/hotfix-session-fix
[branch "hotfix/pydantic-fixes"]
	remote = origin
	merge = refs/heads/hotfix/pydantic-fixes
[branch "PROM-18942"]
	remote = origin
	merge = refs/heads/PROM-18942
[branch "hotfix-job-repo"]
	remote = origin
	merge = refs/heads/hotfix-job-repo
